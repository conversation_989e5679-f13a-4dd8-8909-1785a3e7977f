// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: islamic/v1/prayer.proto

package islamicv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 日历查询请求
type CalendarReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Year           int32                  `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty" dc:"公历年份"`                                                      // 公历年份
	Month          int32                  `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty" dc:"公历月份"`                                                    // 公历月份
	MethodCode     string                 `protobuf:"bytes,3,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法：AUTO, LFNU, UMMUL_QURA"`  // 计算方法：AUTO, LFNU, UMMUL_QURA
	DateAdjustment int32                  `protobuf:"varint,4,opt,name=date_adjustment,json=dateAdjustment,proto3" json:"date_adjustment,omitempty" dc:"日期校正：-3到+3天的偏移量"` // 日期校正：-3到+3天的偏移量
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CalendarReq) Reset() {
	*x = CalendarReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarReq) ProtoMessage() {}

func (x *CalendarReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarReq.ProtoReflect.Descriptor instead.
func (*CalendarReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{0}
}

func (x *CalendarReq) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *CalendarReq) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *CalendarReq) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *CalendarReq) GetDateAdjustment() int32 {
	if x != nil {
		return x.DateAdjustment
	}
	return 0
}

// 批量日历查询请求
type BatchCalendarReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	YearMonths     []string               `protobuf:"bytes,1,rep,name=year_months,json=yearMonths,proto3" json:"year_months,omitempty" dc:"年月列表，格式：'YYYY-MM'，如：'2025-05'"` // 年月列表，格式："YYYY-MM"，如："2025-05"
	MethodCode     string                 `protobuf:"bytes,2,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法：AUTO, LFNU, UMMUL_QURA"`   // 计算方法：AUTO, LFNU, UMMUL_QURA
	DateAdjustment int32                  `protobuf:"varint,3,opt,name=date_adjustment,json=dateAdjustment,proto3" json:"date_adjustment,omitempty" dc:"日期校正：-3到+3天的偏移量"`  // 日期校正：-3到+3天的偏移量
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BatchCalendarReq) Reset() {
	*x = BatchCalendarReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCalendarReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCalendarReq) ProtoMessage() {}

func (x *BatchCalendarReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCalendarReq.ProtoReflect.Descriptor instead.
func (*BatchCalendarReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{1}
}

func (x *BatchCalendarReq) GetYearMonths() []string {
	if x != nil {
		return x.YearMonths
	}
	return nil
}

func (x *BatchCalendarReq) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *BatchCalendarReq) GetDateAdjustment() int32 {
	if x != nil {
		return x.DateAdjustment
	}
	return 0
}

// 日历日期信息
type CalendarDateInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	GregorianYear  int32                  `protobuf:"varint,1,opt,name=gregorian_year,json=gregorianYear,proto3" json:"gregorian_year,omitempty" dc:"公历年"`                                                                                                                                            // 公历年
	GregorianMonth int32                  `protobuf:"varint,2,opt,name=gregorian_month,json=gregorianMonth,proto3" json:"gregorian_month,omitempty" dc:"公历月"`                                                                                                                                         // 公历月
	GregorianDay   int32                  `protobuf:"varint,3,opt,name=gregorian_day,json=gregorianDay,proto3" json:"gregorian_day,omitempty" dc:"公历日"`                                                                                                                                               // 公历日
	HijriahYear    int32                  `protobuf:"varint,4,opt,name=hijriah_year,json=hijriahYear,proto3" json:"hijriah_year,omitempty" dc:"Hijriah年"`                                                                                                                                             // Hijriah年
	HijriahMonth   int32                  `protobuf:"varint,5,opt,name=hijriah_month,json=hijriahMonth,proto3" json:"hijriah_month,omitempty" dc:"Hijriah月 1-12 (Muharam, Safar, Rabiul Awal, Rabiul Akhir, Jumadal Ula, Jumadal Akhirah, Rajab, Sya'ban, Ramadhan, Syawal, Dzulqa'dah, Dzulhijjah)"` // Hijriah月 1-12 (Muharam, Safar, Rabiul Awal, Rabiul Akhir, Jumadal Ula, Jumadal Akhirah, Rajab, Sya'ban, Ramadhan, Syawal, Dzulqa'dah, Dzulhijjah)
	HijriahDay     int32                  `protobuf:"varint,6,opt,name=hijriah_day,json=hijriahDay,proto3" json:"hijriah_day,omitempty" dc:"Hijriah日"`                                                                                                                                                // Hijriah日
	MethodCode     string                 `protobuf:"bytes,7,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法代码"`                                                                                                                                                   // 计算方法代码
	Weekday        int32                  `protobuf:"varint,8,opt,name=weekday,proto3" json:"weekday,omitempty" dc:"星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)"`                                                                                                                                    // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
	Pasaran        int32                  `protobuf:"varint,9,opt,name=pasaran,proto3" json:"pasaran,omitempty" dc:"Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)"`                                                                                                                         // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
	WeekdayName    string                 `protobuf:"bytes,10,opt,name=weekday_name,json=weekdayName,proto3" json:"weekday_name,omitempty" dc:"星期名称（本地化）(Ahad Senin Selasa Rabu Kamis Jumat Sabtu)"`                                                                                                  // 星期名称（本地化）(Ahad Senin Selasa Rabu Kamis Jumat Sabtu)
	PasaranName    string                 `protobuf:"bytes,11,opt,name=pasaran_name,json=pasaranName,proto3" json:"pasaran_name,omitempty" dc:"Pasaran名称（本地化）"`                                                                                                                                       // Pasaran名称（本地化）
	Events         []*CalendarEventInfo   `protobuf:"bytes,12,rep,name=events,proto3" json:"events,omitempty" dc:"当日事件列表"`                                                                                                                                                                            // 当日事件列表
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CalendarDateInfo) Reset() {
	*x = CalendarDateInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarDateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarDateInfo) ProtoMessage() {}

func (x *CalendarDateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarDateInfo.ProtoReflect.Descriptor instead.
func (*CalendarDateInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{2}
}

func (x *CalendarDateInfo) GetGregorianYear() int32 {
	if x != nil {
		return x.GregorianYear
	}
	return 0
}

func (x *CalendarDateInfo) GetGregorianMonth() int32 {
	if x != nil {
		return x.GregorianMonth
	}
	return 0
}

func (x *CalendarDateInfo) GetGregorianDay() int32 {
	if x != nil {
		return x.GregorianDay
	}
	return 0
}

func (x *CalendarDateInfo) GetHijriahYear() int32 {
	if x != nil {
		return x.HijriahYear
	}
	return 0
}

func (x *CalendarDateInfo) GetHijriahMonth() int32 {
	if x != nil {
		return x.HijriahMonth
	}
	return 0
}

func (x *CalendarDateInfo) GetHijriahDay() int32 {
	if x != nil {
		return x.HijriahDay
	}
	return 0
}

func (x *CalendarDateInfo) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *CalendarDateInfo) GetWeekday() int32 {
	if x != nil {
		return x.Weekday
	}
	return 0
}

func (x *CalendarDateInfo) GetPasaran() int32 {
	if x != nil {
		return x.Pasaran
	}
	return 0
}

func (x *CalendarDateInfo) GetWeekdayName() string {
	if x != nil {
		return x.WeekdayName
	}
	return ""
}

func (x *CalendarDateInfo) GetPasaranName() string {
	if x != nil {
		return x.PasaranName
	}
	return ""
}

func (x *CalendarDateInfo) GetEvents() []*CalendarEventInfo {
	if x != nil {
		return x.Events
	}
	return nil
}

// 日历事件信息
type CalendarEventInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"事件ID"`                                                                 // 事件ID
	EventType     string                 `protobuf:"bytes,2,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty" dc:"事件类型：HARI_BESAR, LIBUR_NASIONAL, PUASA"` // 事件类型：HARI_BESAR, LIBUR_NASIONAL, PUASA
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"事件标题"`                                                            // 事件标题
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" dc:"事件描述"`                                                // 事件描述
	JumpUrl       string                 `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty" dc:"点击跳转链接"`                                       // 点击跳转链接
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalendarEventInfo) Reset() {
	*x = CalendarEventInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarEventInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarEventInfo) ProtoMessage() {}

func (x *CalendarEventInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarEventInfo.ProtoReflect.Descriptor instead.
func (*CalendarEventInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{3}
}

func (x *CalendarEventInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CalendarEventInfo) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *CalendarEventInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CalendarEventInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CalendarEventInfo) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

// 日历数据
type CalendarData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*CalendarDateInfo    `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"日历数据列表"` // 日历数据列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalendarData) Reset() {
	*x = CalendarData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarData) ProtoMessage() {}

func (x *CalendarData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarData.ProtoReflect.Descriptor instead.
func (*CalendarData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{4}
}

func (x *CalendarData) GetList() []*CalendarDateInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 日历响应
type CalendarRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *CalendarData          `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalendarRes) Reset() {
	*x = CalendarRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarRes) ProtoMessage() {}

func (x *CalendarRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarRes.ProtoReflect.Descriptor instead.
func (*CalendarRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{5}
}

func (x *CalendarRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CalendarRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CalendarRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CalendarRes) GetData() *CalendarData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 批量日历响应
type BatchCalendarRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *BatchCalendarData     `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCalendarRes) Reset() {
	*x = BatchCalendarRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCalendarRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCalendarRes) ProtoMessage() {}

func (x *BatchCalendarRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCalendarRes.ProtoReflect.Descriptor instead.
func (*BatchCalendarRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{6}
}

func (x *BatchCalendarRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BatchCalendarRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BatchCalendarRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BatchCalendarRes) GetData() *BatchCalendarData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 批量日历数据
type BatchCalendarData struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Calendars     map[string]*CalendarData `protobuf:"bytes,1,rep,name=calendars,proto3" json:"calendars,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value" dc:"日历数据映射，key为'YYYY-MM'格式，value为对应月份的日历数据"` // 日历数据映射，key为"YYYY-MM"格式，value为对应月份的日历数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCalendarData) Reset() {
	*x = BatchCalendarData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCalendarData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCalendarData) ProtoMessage() {}

func (x *BatchCalendarData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCalendarData.ProtoReflect.Descriptor instead.
func (*BatchCalendarData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{7}
}

func (x *BatchCalendarData) GetCalendars() map[string]*CalendarData {
	if x != nil {
		return x.Calendars
	}
	return nil
}

// 祷告时间查询请求
type GetDailyPrayerTimeReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Date           string                 `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty" dc:"日期 YYYY-MM-DD 格式"`                                                                 // 日期 YYYY-MM-DD 格式
	Latitude       float64                `protobuf:"fixed64,2,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度"`                                                                     // 纬度
	Longitude      float64                `protobuf:"fixed64,3,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度"`                                                                   // 经度
	Timezone       string                 `protobuf:"bytes,4,opt,name=timezone,proto3" json:"timezone,omitempty" dc:"时区，如 'Asia/Shanghai'"`                                                     // 时区，如 "Asia/Shanghai"
	MethodCode     string                 `protobuf:"bytes,5,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法：AUTO, LFNU, UMMUL_QURA (这个设置是在日历那边，用于返回伊斯兰日期)"` // 计算方法：AUTO, LFNU, UMMUL_QURA (这个设置是在日历那边，用于返回伊斯兰日期)
	DateAdjustment int32                  `protobuf:"varint,6,opt,name=date_adjustment,json=dateAdjustment,proto3" json:"date_adjustment,omitempty" dc:"日期校正：-3到+3天的偏移量 (这个设置是在日历那边)"`          // 日期校正：-3到+3天的偏移量 (这个设置是在日历那边)
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetDailyPrayerTimeReq) Reset() {
	*x = GetDailyPrayerTimeReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDailyPrayerTimeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyPrayerTimeReq) ProtoMessage() {}

func (x *GetDailyPrayerTimeReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyPrayerTimeReq.ProtoReflect.Descriptor instead.
func (*GetDailyPrayerTimeReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{8}
}

func (x *GetDailyPrayerTimeReq) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *GetDailyPrayerTimeReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *GetDailyPrayerTimeReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *GetDailyPrayerTimeReq) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *GetDailyPrayerTimeReq) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *GetDailyPrayerTimeReq) GetDateAdjustment() int32 {
	if x != nil {
		return x.DateAdjustment
	}
	return 0
}

// 祷告时间查询响应
type GetDailyPrayerTimeRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *PrayerTimeData        `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDailyPrayerTimeRes) Reset() {
	*x = GetDailyPrayerTimeRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDailyPrayerTimeRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyPrayerTimeRes) ProtoMessage() {}

func (x *GetDailyPrayerTimeRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyPrayerTimeRes.ProtoReflect.Descriptor instead.
func (*GetDailyPrayerTimeRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{9}
}

func (x *GetDailyPrayerTimeRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetDailyPrayerTimeRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetDailyPrayerTimeRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetDailyPrayerTimeRes) GetData() *PrayerTimeData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 每日祷告时间数据
type PrayerTimeData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Date          string                 `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty" dc:"日期 YYYY-MM-DD 格式"`                        // 日期 YYYY-MM-DD 格式
	PrayerTime    *PrayerTime            `protobuf:"bytes,2,opt,name=prayer_time,json=prayerTime,proto3" json:"prayer_time,omitempty" dc:"祷告时间"`      // 祷告时间
	IslamicDate   *IslamicDate           `protobuf:"bytes,3,opt,name=islamic_date,json=islamicDate,proto3" json:"islamic_date,omitempty" dc:"伊斯兰历日期"` // 伊斯兰历日期
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrayerTimeData) Reset() {
	*x = PrayerTimeData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrayerTimeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrayerTimeData) ProtoMessage() {}

func (x *PrayerTimeData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrayerTimeData.ProtoReflect.Descriptor instead.
func (*PrayerTimeData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{10}
}

func (x *PrayerTimeData) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *PrayerTimeData) GetPrayerTime() *PrayerTime {
	if x != nil {
		return x.PrayerTime
	}
	return nil
}

func (x *PrayerTimeData) GetIslamicDate() *IslamicDate {
	if x != nil {
		return x.IslamicDate
	}
	return nil
}

// 祷告时间
type PrayerTime struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Imsak         string                 `protobuf:"bytes,1,opt,name=imsak,proto3" json:"imsak,omitempty" dc:"伊姆萨克时间（仅斋月期间）"`     // 伊姆萨克时间（仅斋月期间）
	Subuh         string                 `protobuf:"bytes,2,opt,name=subuh,proto3" json:"subuh,omitempty" dc:"晨祷时间"`              // 晨祷时间
	Terbit        string                 `protobuf:"bytes,3,opt,name=terbit,proto3" json:"terbit,omitempty" dc:"日出时间"`            // 日出时间
	Dhuha         string                 `protobuf:"bytes,4,opt,name=dhuha,proto3" json:"dhuha,omitempty" dc:"上午祷告时间（可选，目前还不准确）"` // 上午祷告时间（可选，目前还不准确）
	Zuhur         string                 `protobuf:"bytes,5,opt,name=zuhur,proto3" json:"zuhur,omitempty" dc:"晌祷时间"`              // 晌祷时间
	Ashar         string                 `protobuf:"bytes,6,opt,name=ashar,proto3" json:"ashar,omitempty" dc:"晡祷时间"`              // 晡祷时间
	Maghrib       string                 `protobuf:"bytes,7,opt,name=maghrib,proto3" json:"maghrib,omitempty" dc:"昏祷时间"`          // 昏祷时间
	Isya          string                 `protobuf:"bytes,8,opt,name=isya,proto3" json:"isya,omitempty" dc:"宵祷时间"`                // 宵祷时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrayerTime) Reset() {
	*x = PrayerTime{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrayerTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrayerTime) ProtoMessage() {}

func (x *PrayerTime) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrayerTime.ProtoReflect.Descriptor instead.
func (*PrayerTime) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{11}
}

func (x *PrayerTime) GetImsak() string {
	if x != nil {
		return x.Imsak
	}
	return ""
}

func (x *PrayerTime) GetSubuh() string {
	if x != nil {
		return x.Subuh
	}
	return ""
}

func (x *PrayerTime) GetTerbit() string {
	if x != nil {
		return x.Terbit
	}
	return ""
}

func (x *PrayerTime) GetDhuha() string {
	if x != nil {
		return x.Dhuha
	}
	return ""
}

func (x *PrayerTime) GetZuhur() string {
	if x != nil {
		return x.Zuhur
	}
	return ""
}

func (x *PrayerTime) GetAshar() string {
	if x != nil {
		return x.Ashar
	}
	return ""
}

func (x *PrayerTime) GetMaghrib() string {
	if x != nil {
		return x.Maghrib
	}
	return ""
}

func (x *PrayerTime) GetIsya() string {
	if x != nil {
		return x.Isya
	}
	return ""
}

// 伊斯兰历日期
type IslamicDate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Year          int32                  `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty" dc:"伊斯兰历年份"`   // 伊斯兰历年份
	Month         int32                  `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty" dc:"伊斯兰历月份"` // 伊斯兰历月份
	Day           int32                  `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty" dc:"伊斯兰历日期"`     // 伊斯兰历日期
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IslamicDate) Reset() {
	*x = IslamicDate{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IslamicDate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IslamicDate) ProtoMessage() {}

func (x *IslamicDate) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IslamicDate.ProtoReflect.Descriptor instead.
func (*IslamicDate) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{12}
}

func (x *IslamicDate) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *IslamicDate) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *IslamicDate) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

// 获取月度祷告时间请求
type GetMonthlyPrayerTimesReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Year           int32                  `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty" dc:"年份 YYYY 格式"`                                                // 年份 YYYY 格式
	Month          int32                  `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty" dc:"月份 1-12"`                                                 // 月份 1-12
	Latitude       float64                `protobuf:"fixed64,3,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度"`                                               // 纬度
	Longitude      float64                `protobuf:"fixed64,4,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度"`                                             // 经度
	Timezone       string                 `protobuf:"bytes,5,opt,name=timezone,proto3" json:"timezone,omitempty" dc:"时区，如 'Asia/Shanghai'"`                               // 时区，如 "Asia/Shanghai"
	MethodCode     string                 `protobuf:"bytes,6,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法：AUTO, LFNU, UMMUL_QURA"`  // 计算方法：AUTO, LFNU, UMMUL_QURA
	DateAdjustment int32                  `protobuf:"varint,7,opt,name=date_adjustment,json=dateAdjustment,proto3" json:"date_adjustment,omitempty" dc:"日期校正：-3到+3天的偏移量"` // 日期校正：-3到+3天的偏移量
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetMonthlyPrayerTimesReq) Reset() {
	*x = GetMonthlyPrayerTimesReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMonthlyPrayerTimesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonthlyPrayerTimesReq) ProtoMessage() {}

func (x *GetMonthlyPrayerTimesReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonthlyPrayerTimesReq.ProtoReflect.Descriptor instead.
func (*GetMonthlyPrayerTimesReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{13}
}

func (x *GetMonthlyPrayerTimesReq) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *GetMonthlyPrayerTimesReq) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *GetMonthlyPrayerTimesReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *GetMonthlyPrayerTimesReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *GetMonthlyPrayerTimesReq) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *GetMonthlyPrayerTimesReq) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *GetMonthlyPrayerTimesReq) GetDateAdjustment() int32 {
	if x != nil {
		return x.DateAdjustment
	}
	return 0
}

// 获取月度祷告时间响应
type GetMonthlyPrayerTimesRes struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Code          int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error           `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *MonthlyPrayerTimesData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMonthlyPrayerTimesRes) Reset() {
	*x = GetMonthlyPrayerTimesRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMonthlyPrayerTimesRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonthlyPrayerTimesRes) ProtoMessage() {}

func (x *GetMonthlyPrayerTimesRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonthlyPrayerTimesRes.ProtoReflect.Descriptor instead.
func (*GetMonthlyPrayerTimesRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{14}
}

func (x *GetMonthlyPrayerTimesRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMonthlyPrayerTimesRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetMonthlyPrayerTimesRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetMonthlyPrayerTimesRes) GetData() *MonthlyPrayerTimesData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 月度祷告时间数据
type MonthlyPrayerTimesData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*PrayerTimeData      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"每日祷告时间列表"` // 每日祷告时间列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MonthlyPrayerTimesData) Reset() {
	*x = MonthlyPrayerTimesData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MonthlyPrayerTimesData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyPrayerTimesData) ProtoMessage() {}

func (x *MonthlyPrayerTimesData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyPrayerTimesData.ProtoReflect.Descriptor instead.
func (*MonthlyPrayerTimesData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{15}
}

func (x *MonthlyPrayerTimesData) GetList() []*PrayerTimeData {
	if x != nil {
		return x.List
	}
	return nil
}

// 朝觐日程列表请求
type HajiJadwalListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiJadwalListReq) Reset() {
	*x = HajiJadwalListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiJadwalListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiJadwalListReq) ProtoMessage() {}

func (x *HajiJadwalListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiJadwalListReq.ProtoReflect.Descriptor instead.
func (*HajiJadwalListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{16}
}

// 朝觐日程详情请求
type HajiJadwalDetailReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JadwalId      uint64                 `protobuf:"varint,1,opt,name=jadwal_id,json=jadwalId,proto3" json:"jadwal_id,omitempty" dc:"朝觐日程ID"` // 朝觐日程ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiJadwalDetailReq) Reset() {
	*x = HajiJadwalDetailReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiJadwalDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiJadwalDetailReq) ProtoMessage() {}

func (x *HajiJadwalDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiJadwalDetailReq.ProtoReflect.Descriptor instead.
func (*HajiJadwalDetailReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{17}
}

func (x *HajiJadwalDetailReq) GetJadwalId() uint64 {
	if x != nil {
		return x.JadwalId
	}
	return 0
}

// 朝觐日程项目信息
type HajiJadwalInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"朝觐日程ID"`                                                                  // 朝觐日程ID
	ItemNo         int32                  `protobuf:"varint,2,opt,name=item_no,json=itemNo,proto3" json:"item_no,omitempty" dc:"项目编号"`                                              // 项目编号
	TimeInfo       string                 `protobuf:"bytes,3,opt,name=time_info,json=timeInfo,proto3" json:"time_info,omitempty" dc:"时间信息"`                                         // 时间信息
	EventSummary   string                 `protobuf:"bytes,4,opt,name=event_summary,json=eventSummary,proto3" json:"event_summary,omitempty" dc:"事件简述"`                             // 事件简述
	AdditionalInfo string                 `protobuf:"bytes,5,opt,name=additional_info,json=additionalInfo,proto3" json:"additional_info,omitempty" dc:"附加信息（请求列表的时候返回空，详情的时候才有内容）"` // 附加信息（请求列表的时候返回空，详情的时候才有内容）
	ArticleText    string                 `protobuf:"bytes,6,opt,name=article_text,json=articleText,proto3" json:"article_text,omitempty" dc:"文章详情（副文本）"`                           // 文章详情（副文本）
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *HajiJadwalInfo) Reset() {
	*x = HajiJadwalInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiJadwalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiJadwalInfo) ProtoMessage() {}

func (x *HajiJadwalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiJadwalInfo.ProtoReflect.Descriptor instead.
func (*HajiJadwalInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{18}
}

func (x *HajiJadwalInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiJadwalInfo) GetItemNo() int32 {
	if x != nil {
		return x.ItemNo
	}
	return 0
}

func (x *HajiJadwalInfo) GetTimeInfo() string {
	if x != nil {
		return x.TimeInfo
	}
	return ""
}

func (x *HajiJadwalInfo) GetEventSummary() string {
	if x != nil {
		return x.EventSummary
	}
	return ""
}

func (x *HajiJadwalInfo) GetAdditionalInfo() string {
	if x != nil {
		return x.AdditionalInfo
	}
	return ""
}

func (x *HajiJadwalInfo) GetArticleText() string {
	if x != nil {
		return x.ArticleText
	}
	return ""
}

// 朝觐日程列表数据
type HajiJadwalListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*HajiJadwalInfo      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"朝觐日程列表"`               // 朝觐日程列表
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty" dc:"日程说明文字"` // 日程说明文字
	Year          string                 `protobuf:"bytes,3,opt,name=year,proto3" json:"year,omitempty" dc:"朝觐年份"`                 // 朝觐年份
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiJadwalListData) Reset() {
	*x = HajiJadwalListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiJadwalListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiJadwalListData) ProtoMessage() {}

func (x *HajiJadwalListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiJadwalListData.ProtoReflect.Descriptor instead.
func (*HajiJadwalListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{19}
}

func (x *HajiJadwalListData) GetList() []*HajiJadwalInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *HajiJadwalListData) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *HajiJadwalListData) GetYear() string {
	if x != nil {
		return x.Year
	}
	return ""
}

// 朝觐日程列表响应
type HajiJadwalListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HajiJadwalListData    `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiJadwalListRes) Reset() {
	*x = HajiJadwalListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiJadwalListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiJadwalListRes) ProtoMessage() {}

func (x *HajiJadwalListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiJadwalListRes.ProtoReflect.Descriptor instead.
func (*HajiJadwalListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{20}
}

func (x *HajiJadwalListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HajiJadwalListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HajiJadwalListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HajiJadwalListRes) GetData() *HajiJadwalListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 朝觐日程详情数据
type HajiJadwalDetailData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jadwal        *HajiJadwalInfo        `protobuf:"bytes,1,opt,name=jadwal,proto3" json:"jadwal,omitempty" dc:"朝觐日程详情"` // 朝觐日程详情
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiJadwalDetailData) Reset() {
	*x = HajiJadwalDetailData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiJadwalDetailData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiJadwalDetailData) ProtoMessage() {}

func (x *HajiJadwalDetailData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiJadwalDetailData.ProtoReflect.Descriptor instead.
func (*HajiJadwalDetailData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{21}
}

func (x *HajiJadwalDetailData) GetJadwal() *HajiJadwalInfo {
	if x != nil {
		return x.Jadwal
	}
	return nil
}

// 朝觐日程详情响应
type HajiJadwalDetailRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HajiJadwalDetailData  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiJadwalDetailRes) Reset() {
	*x = HajiJadwalDetailRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiJadwalDetailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiJadwalDetailRes) ProtoMessage() {}

func (x *HajiJadwalDetailRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiJadwalDetailRes.ProtoReflect.Descriptor instead.
func (*HajiJadwalDetailRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{22}
}

func (x *HajiJadwalDetailRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HajiJadwalDetailRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HajiJadwalDetailRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HajiJadwalDetailRes) GetData() *HajiJadwalDetailData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 朝觐仪式顺序列表请求
type HajiUrutanListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiUrutanListReq) Reset() {
	*x = HajiUrutanListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiUrutanListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiUrutanListReq) ProtoMessage() {}

func (x *HajiUrutanListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiUrutanListReq.ProtoReflect.Descriptor instead.
func (*HajiUrutanListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{23}
}

// 朝觐仪式顺序详情请求
type HajiUrutanDetailReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UrutanId      uint64                 `protobuf:"varint,1,opt,name=urutan_id,json=urutanId,proto3" json:"urutan_id,omitempty" dc:"朝觐仪式顺序ID"` // 朝觐仪式顺序ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiUrutanDetailReq) Reset() {
	*x = HajiUrutanDetailReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiUrutanDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiUrutanDetailReq) ProtoMessage() {}

func (x *HajiUrutanDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiUrutanDetailReq.ProtoReflect.Descriptor instead.
func (*HajiUrutanDetailReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{24}
}

func (x *HajiUrutanDetailReq) GetUrutanId() uint64 {
	if x != nil {
		return x.UrutanId
	}
	return 0
}

// 朝觐仪式顺序项目信息
type HajiUrutanInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"朝觐仪式顺序ID"`                                                        // 朝觐仪式顺序ID
	UrutanNo      int32                  `protobuf:"varint,2,opt,name=urutan_no,json=urutanNo,proto3" json:"urutan_no,omitempty" dc:"仪式顺序编号"`                              // 仪式顺序编号
	UrutanName    string                 `protobuf:"bytes,3,opt,name=urutan_name,json=urutanName,proto3" json:"urutan_name,omitempty" dc:"仪式名称"`                           // 仪式名称
	UrutanTime    string                 `protobuf:"bytes,4,opt,name=urutan_time,json=urutanTime,proto3" json:"urutan_time,omitempty" dc:"仪式时间信息"`                         // 仪式时间信息
	IconUrl       string                 `protobuf:"bytes,5,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty" dc:"图标URL"`                                   // 图标URL
	UrutanContent string                 `protobuf:"bytes,6,opt,name=urutan_content,json=urutanContent,proto3" json:"urutan_content,omitempty" dc:"仪式内容描述（列表时为空，详情时才有内容）"` // 仪式内容描述（列表时为空，详情时才有内容）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiUrutanInfo) Reset() {
	*x = HajiUrutanInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiUrutanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiUrutanInfo) ProtoMessage() {}

func (x *HajiUrutanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiUrutanInfo.ProtoReflect.Descriptor instead.
func (*HajiUrutanInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{25}
}

func (x *HajiUrutanInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiUrutanInfo) GetUrutanNo() int32 {
	if x != nil {
		return x.UrutanNo
	}
	return 0
}

func (x *HajiUrutanInfo) GetUrutanName() string {
	if x != nil {
		return x.UrutanName
	}
	return ""
}

func (x *HajiUrutanInfo) GetUrutanTime() string {
	if x != nil {
		return x.UrutanTime
	}
	return ""
}

func (x *HajiUrutanInfo) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *HajiUrutanInfo) GetUrutanContent() string {
	if x != nil {
		return x.UrutanContent
	}
	return ""
}

// 朝觐仪式顺序列表数据
type HajiUrutanListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*HajiUrutanInfo      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"朝觐仪式顺序列表"` // 朝觐仪式顺序列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiUrutanListData) Reset() {
	*x = HajiUrutanListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiUrutanListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiUrutanListData) ProtoMessage() {}

func (x *HajiUrutanListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiUrutanListData.ProtoReflect.Descriptor instead.
func (*HajiUrutanListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{26}
}

func (x *HajiUrutanListData) GetList() []*HajiUrutanInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 朝觐仪式顺序列表响应
type HajiUrutanListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HajiUrutanListData    `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiUrutanListRes) Reset() {
	*x = HajiUrutanListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiUrutanListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiUrutanListRes) ProtoMessage() {}

func (x *HajiUrutanListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiUrutanListRes.ProtoReflect.Descriptor instead.
func (*HajiUrutanListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{27}
}

func (x *HajiUrutanListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HajiUrutanListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HajiUrutanListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HajiUrutanListRes) GetData() *HajiUrutanListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 朝觐仪式顺序详情数据
type HajiUrutanDetailData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Urutan        *HajiUrutanInfo        `protobuf:"bytes,1,opt,name=urutan,proto3" json:"urutan,omitempty" dc:"朝觐仪式顺序详情"` // 朝觐仪式顺序详情
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiUrutanDetailData) Reset() {
	*x = HajiUrutanDetailData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiUrutanDetailData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiUrutanDetailData) ProtoMessage() {}

func (x *HajiUrutanDetailData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiUrutanDetailData.ProtoReflect.Descriptor instead.
func (*HajiUrutanDetailData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{28}
}

func (x *HajiUrutanDetailData) GetUrutan() *HajiUrutanInfo {
	if x != nil {
		return x.Urutan
	}
	return nil
}

// 朝觐仪式顺序详情响应
type HajiUrutanDetailRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HajiUrutanDetailData  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiUrutanDetailRes) Reset() {
	*x = HajiUrutanDetailRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiUrutanDetailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiUrutanDetailRes) ProtoMessage() {}

func (x *HajiUrutanDetailRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiUrutanDetailRes.ProtoReflect.Descriptor instead.
func (*HajiUrutanDetailRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{29}
}

func (x *HajiUrutanDetailRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HajiUrutanDetailRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HajiUrutanDetailRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HajiUrutanDetailRes) GetData() *HajiUrutanDetailData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 副朝仪式顺序列表请求
type UmrahUrutanListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahUrutanListReq) Reset() {
	*x = UmrahUrutanListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahUrutanListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahUrutanListReq) ProtoMessage() {}

func (x *UmrahUrutanListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahUrutanListReq.ProtoReflect.Descriptor instead.
func (*UmrahUrutanListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{30}
}

// 副朝仪式顺序详情请求
type UmrahUrutanDetailReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UrutanId      uint64                 `protobuf:"varint,1,opt,name=urutan_id,json=urutanId,proto3" json:"urutan_id,omitempty" dc:"副朝仪式顺序ID"` // 副朝仪式顺序ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahUrutanDetailReq) Reset() {
	*x = UmrahUrutanDetailReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahUrutanDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahUrutanDetailReq) ProtoMessage() {}

func (x *UmrahUrutanDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahUrutanDetailReq.ProtoReflect.Descriptor instead.
func (*UmrahUrutanDetailReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{31}
}

func (x *UmrahUrutanDetailReq) GetUrutanId() uint64 {
	if x != nil {
		return x.UrutanId
	}
	return 0
}

// 副朝仪式顺序项目信息
type UmrahUrutanInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"副朝仪式顺序ID"`                                                        // 副朝仪式顺序ID
	UrutanNo      int32                  `protobuf:"varint,2,opt,name=urutan_no,json=urutanNo,proto3" json:"urutan_no,omitempty" dc:"仪式顺序编号"`                              // 仪式顺序编号
	UrutanName    string                 `protobuf:"bytes,3,opt,name=urutan_name,json=urutanName,proto3" json:"urutan_name,omitempty" dc:"仪式名称"`                           // 仪式名称
	UrutanTime    string                 `protobuf:"bytes,4,opt,name=urutan_time,json=urutanTime,proto3" json:"urutan_time,omitempty" dc:"仪式时间信息"`                         // 仪式时间信息
	IconUrl       string                 `protobuf:"bytes,5,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty" dc:"图标URL"`                                   // 图标URL
	UrutanContent string                 `protobuf:"bytes,6,opt,name=urutan_content,json=urutanContent,proto3" json:"urutan_content,omitempty" dc:"仪式内容描述（列表时为空，详情时才有内容）"` // 仪式内容描述（列表时为空，详情时才有内容）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahUrutanInfo) Reset() {
	*x = UmrahUrutanInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahUrutanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahUrutanInfo) ProtoMessage() {}

func (x *UmrahUrutanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahUrutanInfo.ProtoReflect.Descriptor instead.
func (*UmrahUrutanInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{32}
}

func (x *UmrahUrutanInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UmrahUrutanInfo) GetUrutanNo() int32 {
	if x != nil {
		return x.UrutanNo
	}
	return 0
}

func (x *UmrahUrutanInfo) GetUrutanName() string {
	if x != nil {
		return x.UrutanName
	}
	return ""
}

func (x *UmrahUrutanInfo) GetUrutanTime() string {
	if x != nil {
		return x.UrutanTime
	}
	return ""
}

func (x *UmrahUrutanInfo) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *UmrahUrutanInfo) GetUrutanContent() string {
	if x != nil {
		return x.UrutanContent
	}
	return ""
}

// 副朝仪式顺序列表数据
type UmrahUrutanListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*UmrahUrutanInfo     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"副朝仪式顺序列表"` // 副朝仪式顺序列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahUrutanListData) Reset() {
	*x = UmrahUrutanListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahUrutanListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahUrutanListData) ProtoMessage() {}

func (x *UmrahUrutanListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahUrutanListData.ProtoReflect.Descriptor instead.
func (*UmrahUrutanListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{33}
}

func (x *UmrahUrutanListData) GetList() []*UmrahUrutanInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 副朝仪式顺序列表响应
type UmrahUrutanListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UmrahUrutanListData   `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahUrutanListRes) Reset() {
	*x = UmrahUrutanListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahUrutanListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahUrutanListRes) ProtoMessage() {}

func (x *UmrahUrutanListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahUrutanListRes.ProtoReflect.Descriptor instead.
func (*UmrahUrutanListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{34}
}

func (x *UmrahUrutanListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UmrahUrutanListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UmrahUrutanListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UmrahUrutanListRes) GetData() *UmrahUrutanListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 副朝仪式顺序详情数据
type UmrahUrutanDetailData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Urutan        *UmrahUrutanInfo       `protobuf:"bytes,1,opt,name=urutan,proto3" json:"urutan,omitempty" dc:"副朝仪式顺序详情"` // 副朝仪式顺序详情
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahUrutanDetailData) Reset() {
	*x = UmrahUrutanDetailData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahUrutanDetailData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahUrutanDetailData) ProtoMessage() {}

func (x *UmrahUrutanDetailData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahUrutanDetailData.ProtoReflect.Descriptor instead.
func (*UmrahUrutanDetailData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{35}
}

func (x *UmrahUrutanDetailData) GetUrutan() *UmrahUrutanInfo {
	if x != nil {
		return x.Urutan
	}
	return nil
}

// 副朝仪式顺序详情响应
type UmrahUrutanDetailRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UmrahUrutanDetailData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahUrutanDetailRes) Reset() {
	*x = UmrahUrutanDetailRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahUrutanDetailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahUrutanDetailRes) ProtoMessage() {}

func (x *UmrahUrutanDetailRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahUrutanDetailRes.ProtoReflect.Descriptor instead.
func (*UmrahUrutanDetailRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{36}
}

func (x *UmrahUrutanDetailRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UmrahUrutanDetailRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UmrahUrutanDetailRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UmrahUrutanDetailRes) GetData() *UmrahUrutanDetailData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 朝觐祈祷文简要列表请求（一次过返回所有数据）
type HajiDoaRingkasListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaRingkasListReq) Reset() {
	*x = HajiDoaRingkasListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaRingkasListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaRingkasListReq) ProtoMessage() {}

func (x *HajiDoaRingkasListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaRingkasListReq.ProtoReflect.Descriptor instead.
func (*HajiDoaRingkasListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{37}
}

// 朝觐祈祷文内容信息
type HajiDoaRingkasContentInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"内容ID"`                                                                // 内容ID
	ContentOrder   int32                  `protobuf:"varint,2,opt,name=content_order,json=contentOrder,proto3" json:"content_order,omitempty" dc:"内容排序"`                        // 内容排序
	Title          string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"内容标题（可为空）。最新的修改：忽略这个字段"`                                         // 内容标题（可为空）。最新的修改：忽略这个字段
	MuqattaAt      string                 `protobuf:"bytes,4,opt,name=muqatta_at,json=muqattaAt,proto3" json:"muqatta_at,omitempty" dc:"Muqattaʿāt断章字母（有则展示，无不展示）最新的修改：忽略这个字段"` // Muqattaʿāt断章字母（有则展示，无不展示）最新的修改：忽略这个字段
	ArabicText     string                 `protobuf:"bytes,5,opt,name=arabic_text,json=arabicText,proto3" json:"arabic_text,omitempty" dc:"阿拉伯文原文"`                             // 阿拉伯文原文
	IndonesianText string                 `protobuf:"bytes,6,opt,name=indonesian_text,json=indonesianText,proto3" json:"indonesian_text,omitempty" dc:"印尼语翻译"`                  // 印尼语翻译
	LatinText      string                 `protobuf:"bytes,7,opt,name=latin_text,json=latinText,proto3" json:"latin_text,omitempty" dc:"拉丁音译文本"`                                // 拉丁音译文本
	ContentType    int32                  `protobuf:"varint,8,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty" dc:"内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级"` // 内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *HajiDoaRingkasContentInfo) Reset() {
	*x = HajiDoaRingkasContentInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaRingkasContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaRingkasContentInfo) ProtoMessage() {}

func (x *HajiDoaRingkasContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaRingkasContentInfo.ProtoReflect.Descriptor instead.
func (*HajiDoaRingkasContentInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{38}
}

func (x *HajiDoaRingkasContentInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiDoaRingkasContentInfo) GetContentOrder() int32 {
	if x != nil {
		return x.ContentOrder
	}
	return 0
}

func (x *HajiDoaRingkasContentInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *HajiDoaRingkasContentInfo) GetMuqattaAt() string {
	if x != nil {
		return x.MuqattaAt
	}
	return ""
}

func (x *HajiDoaRingkasContentInfo) GetArabicText() string {
	if x != nil {
		return x.ArabicText
	}
	return ""
}

func (x *HajiDoaRingkasContentInfo) GetIndonesianText() string {
	if x != nil {
		return x.IndonesianText
	}
	return ""
}

func (x *HajiDoaRingkasContentInfo) GetLatinText() string {
	if x != nil {
		return x.LatinText
	}
	return ""
}

func (x *HajiDoaRingkasContentInfo) GetContentType() int32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

// 朝觐祈祷文简要信息
type HajiDoaRingkasInfo struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Id            uint64                       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"祈祷文ID"`                                                          // 祈祷文ID
	DoaNo         int32                        `protobuf:"varint,2,opt,name=doa_no,json=doaNo,proto3" json:"doa_no,omitempty" dc:"祈祷文序号"`                                       // 祈祷文序号
	DoaName       string                       `protobuf:"bytes,3,opt,name=doa_name,json=doaName,proto3" json:"doa_name,omitempty" dc:"祈祷文名称"`                                  // 祈祷文名称
	Contents      []*HajiDoaRingkasContentInfo `protobuf:"bytes,4,rep,name=contents,proto3" json:"contents,omitempty" dc:"祈祷文内容列表"`                                             // 祈祷文内容列表
	BacaanId      uint64                       `protobuf:"varint,5,opt,name=bacaan_id,json=bacaanId,proto3" json:"bacaan_id,omitempty" dc:"诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)"` // 诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)
	IsCollected   bool                         `protobuf:"varint,6,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty" dc:"是否已收藏"`                     // 是否已收藏
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaRingkasInfo) Reset() {
	*x = HajiDoaRingkasInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaRingkasInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaRingkasInfo) ProtoMessage() {}

func (x *HajiDoaRingkasInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaRingkasInfo.ProtoReflect.Descriptor instead.
func (*HajiDoaRingkasInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{39}
}

func (x *HajiDoaRingkasInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiDoaRingkasInfo) GetDoaNo() int32 {
	if x != nil {
		return x.DoaNo
	}
	return 0
}

func (x *HajiDoaRingkasInfo) GetDoaName() string {
	if x != nil {
		return x.DoaName
	}
	return ""
}

func (x *HajiDoaRingkasInfo) GetContents() []*HajiDoaRingkasContentInfo {
	if x != nil {
		return x.Contents
	}
	return nil
}

func (x *HajiDoaRingkasInfo) GetBacaanId() uint64 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *HajiDoaRingkasInfo) GetIsCollected() bool {
	if x != nil {
		return x.IsCollected
	}
	return false
}

// 朝觐祈祷文简要列表数据
type HajiDoaRingkasListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*HajiDoaRingkasInfo  `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"祈祷文简要列表"` // 祈祷文简要列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaRingkasListData) Reset() {
	*x = HajiDoaRingkasListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaRingkasListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaRingkasListData) ProtoMessage() {}

func (x *HajiDoaRingkasListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaRingkasListData.ProtoReflect.Descriptor instead.
func (*HajiDoaRingkasListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{40}
}

func (x *HajiDoaRingkasListData) GetList() []*HajiDoaRingkasInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 朝觐祈祷文简要列表响应
type HajiDoaRingkasListRes struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Code          int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error           `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HajiDoaRingkasListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaRingkasListRes) Reset() {
	*x = HajiDoaRingkasListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaRingkasListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaRingkasListRes) ProtoMessage() {}

func (x *HajiDoaRingkasListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaRingkasListRes.ProtoReflect.Descriptor instead.
func (*HajiDoaRingkasListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{41}
}

func (x *HajiDoaRingkasListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HajiDoaRingkasListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HajiDoaRingkasListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HajiDoaRingkasListRes) GetData() *HajiDoaRingkasListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 朝觐祈祷文详细列表请求
type HajiDoaPanjangListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaPanjangListReq) Reset() {
	*x = HajiDoaPanjangListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaPanjangListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaPanjangListReq) ProtoMessage() {}

func (x *HajiDoaPanjangListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaPanjangListReq.ProtoReflect.Descriptor instead.
func (*HajiDoaPanjangListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{42}
}

// 朝觐祈祷文详细信息
type HajiDoaPanjangInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"祈祷文ID"`                                    // 祈祷文ID
	DoaNo         int32                  `protobuf:"varint,2,opt,name=doa_no,json=doaNo,proto3" json:"doa_no,omitempty" dc:"祈祷文序号"`                 // 祈祷文序号
	DoaName       string                 `protobuf:"bytes,3,opt,name=doa_name,json=doaName,proto3" json:"doa_name,omitempty" dc:"祈祷文名称"`            // 祈祷文名称
	BacaanCount   int32                  `protobuf:"varint,4,opt,name=bacaan_count,json=bacaanCount,proto3" json:"bacaan_count,omitempty" dc:"诵读数"` // 诵读数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaPanjangInfo) Reset() {
	*x = HajiDoaPanjangInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaPanjangInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaPanjangInfo) ProtoMessage() {}

func (x *HajiDoaPanjangInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaPanjangInfo.ProtoReflect.Descriptor instead.
func (*HajiDoaPanjangInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{43}
}

func (x *HajiDoaPanjangInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiDoaPanjangInfo) GetDoaNo() int32 {
	if x != nil {
		return x.DoaNo
	}
	return 0
}

func (x *HajiDoaPanjangInfo) GetDoaName() string {
	if x != nil {
		return x.DoaName
	}
	return ""
}

func (x *HajiDoaPanjangInfo) GetBacaanCount() int32 {
	if x != nil {
		return x.BacaanCount
	}
	return 0
}

// 朝觐祈祷文详细列表数据
type HajiDoaPanjangListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*HajiDoaPanjangInfo  `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"祈祷文详细列表"` // 祈祷文详细列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaPanjangListData) Reset() {
	*x = HajiDoaPanjangListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaPanjangListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaPanjangListData) ProtoMessage() {}

func (x *HajiDoaPanjangListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaPanjangListData.ProtoReflect.Descriptor instead.
func (*HajiDoaPanjangListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{44}
}

func (x *HajiDoaPanjangListData) GetList() []*HajiDoaPanjangInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 朝觐祈祷文详细列表响应
type HajiDoaPanjangListRes struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Code          int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error           `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HajiDoaPanjangListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaPanjangListRes) Reset() {
	*x = HajiDoaPanjangListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaPanjangListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaPanjangListRes) ProtoMessage() {}

func (x *HajiDoaPanjangListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaPanjangListRes.ProtoReflect.Descriptor instead.
func (*HajiDoaPanjangListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{45}
}

func (x *HajiDoaPanjangListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HajiDoaPanjangListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HajiDoaPanjangListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HajiDoaPanjangListRes) GetData() *HajiDoaPanjangListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 朝觐祈祷文诵读内容请求
type HajiDoaPanjangBacaanListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DoaId         uint64                 `protobuf:"varint,1,opt,name=doa_id,json=doaId,proto3" json:"doa_id,omitempty" dc:"祈祷文ID"` // 祈祷文ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaPanjangBacaanListReq) Reset() {
	*x = HajiDoaPanjangBacaanListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaPanjangBacaanListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaPanjangBacaanListReq) ProtoMessage() {}

func (x *HajiDoaPanjangBacaanListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaPanjangBacaanListReq.ProtoReflect.Descriptor instead.
func (*HajiDoaPanjangBacaanListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{46}
}

func (x *HajiDoaPanjangBacaanListReq) GetDoaId() uint64 {
	if x != nil {
		return x.DoaId
	}
	return 0
}

// 朝觐祈祷文诵读内容信息
type HajiDoaPanjangBacaanContentInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"内容ID"`                                                                // 内容ID
	ContentOrder   int32                  `protobuf:"varint,2,opt,name=content_order,json=contentOrder,proto3" json:"content_order,omitempty" dc:"内容排序"`                        // 内容排序
	Title          string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"内容标题（可为空）"`                                                      // 内容标题（可为空）
	MuqattaAt      string                 `protobuf:"bytes,4,opt,name=muqatta_at,json=muqattaAt,proto3" json:"muqatta_at,omitempty" dc:"Muqattaʿāt断章字母（有则展示，无不展示）"`             // Muqattaʿāt断章字母（有则展示，无不展示）
	ArabicText     string                 `protobuf:"bytes,5,opt,name=arabic_text,json=arabicText,proto3" json:"arabic_text,omitempty" dc:"阿拉伯文原文"`                             // 阿拉伯文原文
	IndonesianText string                 `protobuf:"bytes,6,opt,name=indonesian_text,json=indonesianText,proto3" json:"indonesian_text,omitempty" dc:"印尼语翻译"`                  // 印尼语翻译
	LatinText      string                 `protobuf:"bytes,7,opt,name=latin_text,json=latinText,proto3" json:"latin_text,omitempty" dc:"拉丁音译文本"`                                // 拉丁音译文本
	ContentType    int32                  `protobuf:"varint,8,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty" dc:"内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级"` // 内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *HajiDoaPanjangBacaanContentInfo) Reset() {
	*x = HajiDoaPanjangBacaanContentInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaPanjangBacaanContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaPanjangBacaanContentInfo) ProtoMessage() {}

func (x *HajiDoaPanjangBacaanContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaPanjangBacaanContentInfo.ProtoReflect.Descriptor instead.
func (*HajiDoaPanjangBacaanContentInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{47}
}

func (x *HajiDoaPanjangBacaanContentInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiDoaPanjangBacaanContentInfo) GetContentOrder() int32 {
	if x != nil {
		return x.ContentOrder
	}
	return 0
}

func (x *HajiDoaPanjangBacaanContentInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *HajiDoaPanjangBacaanContentInfo) GetMuqattaAt() string {
	if x != nil {
		return x.MuqattaAt
	}
	return ""
}

func (x *HajiDoaPanjangBacaanContentInfo) GetArabicText() string {
	if x != nil {
		return x.ArabicText
	}
	return ""
}

func (x *HajiDoaPanjangBacaanContentInfo) GetIndonesianText() string {
	if x != nil {
		return x.IndonesianText
	}
	return ""
}

func (x *HajiDoaPanjangBacaanContentInfo) GetLatinText() string {
	if x != nil {
		return x.LatinText
	}
	return ""
}

func (x *HajiDoaPanjangBacaanContentInfo) GetContentType() int32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

// 朝觐祈祷文诵读信息
type HajiDoaPanjangBacaanInfo struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Id            uint64                             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"诵读ID"`                                                           // 诵读ID
	DoaId         uint64                             `protobuf:"varint,2,opt,name=doa_id,json=doaId,proto3" json:"doa_id,omitempty" dc:"祈祷文ID"`                                       // 祈祷文ID
	BacaanNo      int32                              `protobuf:"varint,3,opt,name=bacaan_no,json=bacaanNo,proto3" json:"bacaan_no,omitempty" dc:"诵读序号"`                               // 诵读序号
	BacaanName    string                             `protobuf:"bytes,4,opt,name=bacaan_name,json=bacaanName,proto3" json:"bacaan_name,omitempty" dc:"诵读名称"`                          // 诵读名称
	Contents      []*HajiDoaPanjangBacaanContentInfo `protobuf:"bytes,5,rep,name=contents,proto3" json:"contents,omitempty" dc:"诵读内容列表"`                                              // 诵读内容列表
	BacaanId      uint64                             `protobuf:"varint,6,opt,name=bacaan_id,json=bacaanId,proto3" json:"bacaan_id,omitempty" dc:"诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)"` // 诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)
	IsCollected   bool                               `protobuf:"varint,7,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty" dc:"是否已收藏"`                     // 是否已收藏
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaPanjangBacaanInfo) Reset() {
	*x = HajiDoaPanjangBacaanInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaPanjangBacaanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaPanjangBacaanInfo) ProtoMessage() {}

func (x *HajiDoaPanjangBacaanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaPanjangBacaanInfo.ProtoReflect.Descriptor instead.
func (*HajiDoaPanjangBacaanInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{48}
}

func (x *HajiDoaPanjangBacaanInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiDoaPanjangBacaanInfo) GetDoaId() uint64 {
	if x != nil {
		return x.DoaId
	}
	return 0
}

func (x *HajiDoaPanjangBacaanInfo) GetBacaanNo() int32 {
	if x != nil {
		return x.BacaanNo
	}
	return 0
}

func (x *HajiDoaPanjangBacaanInfo) GetBacaanName() string {
	if x != nil {
		return x.BacaanName
	}
	return ""
}

func (x *HajiDoaPanjangBacaanInfo) GetContents() []*HajiDoaPanjangBacaanContentInfo {
	if x != nil {
		return x.Contents
	}
	return nil
}

func (x *HajiDoaPanjangBacaanInfo) GetBacaanId() uint64 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *HajiDoaPanjangBacaanInfo) GetIsCollected() bool {
	if x != nil {
		return x.IsCollected
	}
	return false
}

// 朝觐祈祷文诵读内容列表数据
type HajiDoaPanjangBacaanListData struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	List          []*HajiDoaPanjangBacaanInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"诵读内容列表"`                       // 诵读内容列表
	DoaNo         int32                       `protobuf:"varint,2,opt,name=doa_no,json=doaNo,proto3" json:"doa_no,omitempty" dc:"所属祈祷文序号"`      // 所属祈祷文序号
	DoaName       string                      `protobuf:"bytes,3,opt,name=doa_name,json=doaName,proto3" json:"doa_name,omitempty" dc:"所属祈祷文名称"` // 所属祈祷文名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaPanjangBacaanListData) Reset() {
	*x = HajiDoaPanjangBacaanListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaPanjangBacaanListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaPanjangBacaanListData) ProtoMessage() {}

func (x *HajiDoaPanjangBacaanListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaPanjangBacaanListData.ProtoReflect.Descriptor instead.
func (*HajiDoaPanjangBacaanListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{49}
}

func (x *HajiDoaPanjangBacaanListData) GetList() []*HajiDoaPanjangBacaanInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *HajiDoaPanjangBacaanListData) GetDoaNo() int32 {
	if x != nil {
		return x.DoaNo
	}
	return 0
}

func (x *HajiDoaPanjangBacaanListData) GetDoaName() string {
	if x != nil {
		return x.DoaName
	}
	return ""
}

// 朝觐祈祷文诵读内容列表响应
type HajiDoaPanjangBacaanListRes struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Code          int32                         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error                 `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HajiDoaPanjangBacaanListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiDoaPanjangBacaanListRes) Reset() {
	*x = HajiDoaPanjangBacaanListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaPanjangBacaanListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaPanjangBacaanListRes) ProtoMessage() {}

func (x *HajiDoaPanjangBacaanListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaPanjangBacaanListRes.ProtoReflect.Descriptor instead.
func (*HajiDoaPanjangBacaanListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{50}
}

func (x *HajiDoaPanjangBacaanListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HajiDoaPanjangBacaanListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HajiDoaPanjangBacaanListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HajiDoaPanjangBacaanListRes) GetData() *HajiDoaPanjangBacaanListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 副朝祈祷文简要列表请求
type UmrahDoaRingkasListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaRingkasListReq) Reset() {
	*x = UmrahDoaRingkasListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaRingkasListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaRingkasListReq) ProtoMessage() {}

func (x *UmrahDoaRingkasListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaRingkasListReq.ProtoReflect.Descriptor instead.
func (*UmrahDoaRingkasListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{51}
}

// 副朝祈祷文内容信息
type UmrahDoaRingkasContentInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"内容ID"`                                                                // 内容ID
	ContentOrder   int32                  `protobuf:"varint,2,opt,name=content_order,json=contentOrder,proto3" json:"content_order,omitempty" dc:"内容排序"`                        // 内容排序
	Title          string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"内容标题（可为空）"`                                                      // 内容标题（可为空）
	MuqattaAt      string                 `protobuf:"bytes,4,opt,name=muqatta_at,json=muqattaAt,proto3" json:"muqatta_at,omitempty" dc:"Muqattaʿāt断章字母（有则展示，无不展示）"`             // Muqattaʿāt断章字母（有则展示，无不展示）
	ArabicText     string                 `protobuf:"bytes,5,opt,name=arabic_text,json=arabicText,proto3" json:"arabic_text,omitempty" dc:"阿拉伯文原文"`                             // 阿拉伯文原文
	IndonesianText string                 `protobuf:"bytes,6,opt,name=indonesian_text,json=indonesianText,proto3" json:"indonesian_text,omitempty" dc:"印尼语翻译"`                  // 印尼语翻译
	LatinText      string                 `protobuf:"bytes,7,opt,name=latin_text,json=latinText,proto3" json:"latin_text,omitempty" dc:"拉丁音译文本"`                                // 拉丁音译文本
	ContentType    int32                  `protobuf:"varint,8,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty" dc:"内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级"` // 内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UmrahDoaRingkasContentInfo) Reset() {
	*x = UmrahDoaRingkasContentInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaRingkasContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaRingkasContentInfo) ProtoMessage() {}

func (x *UmrahDoaRingkasContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaRingkasContentInfo.ProtoReflect.Descriptor instead.
func (*UmrahDoaRingkasContentInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{52}
}

func (x *UmrahDoaRingkasContentInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UmrahDoaRingkasContentInfo) GetContentOrder() int32 {
	if x != nil {
		return x.ContentOrder
	}
	return 0
}

func (x *UmrahDoaRingkasContentInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UmrahDoaRingkasContentInfo) GetMuqattaAt() string {
	if x != nil {
		return x.MuqattaAt
	}
	return ""
}

func (x *UmrahDoaRingkasContentInfo) GetArabicText() string {
	if x != nil {
		return x.ArabicText
	}
	return ""
}

func (x *UmrahDoaRingkasContentInfo) GetIndonesianText() string {
	if x != nil {
		return x.IndonesianText
	}
	return ""
}

func (x *UmrahDoaRingkasContentInfo) GetLatinText() string {
	if x != nil {
		return x.LatinText
	}
	return ""
}

func (x *UmrahDoaRingkasContentInfo) GetContentType() int32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

// 副朝祈祷文简要信息
type UmrahDoaRingkasInfo struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Id            uint64                        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"祈祷文ID"`                                                          // 祈祷文ID
	DoaNo         int32                         `protobuf:"varint,2,opt,name=doa_no,json=doaNo,proto3" json:"doa_no,omitempty" dc:"祈祷文序号"`                                       // 祈祷文序号
	DoaName       string                        `protobuf:"bytes,3,opt,name=doa_name,json=doaName,proto3" json:"doa_name,omitempty" dc:"祈祷文名称"`                                  // 祈祷文名称
	Contents      []*UmrahDoaRingkasContentInfo `protobuf:"bytes,4,rep,name=contents,proto3" json:"contents,omitempty" dc:"祈祷文内容列表"`                                             // 祈祷文内容列表
	BacaanId      uint64                        `protobuf:"varint,5,opt,name=bacaan_id,json=bacaanId,proto3" json:"bacaan_id,omitempty" dc:"诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)"` // 诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)
	IsCollected   bool                          `protobuf:"varint,6,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty" dc:"是否已收藏"`                     // 是否已收藏
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaRingkasInfo) Reset() {
	*x = UmrahDoaRingkasInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaRingkasInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaRingkasInfo) ProtoMessage() {}

func (x *UmrahDoaRingkasInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaRingkasInfo.ProtoReflect.Descriptor instead.
func (*UmrahDoaRingkasInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{53}
}

func (x *UmrahDoaRingkasInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UmrahDoaRingkasInfo) GetDoaNo() int32 {
	if x != nil {
		return x.DoaNo
	}
	return 0
}

func (x *UmrahDoaRingkasInfo) GetDoaName() string {
	if x != nil {
		return x.DoaName
	}
	return ""
}

func (x *UmrahDoaRingkasInfo) GetContents() []*UmrahDoaRingkasContentInfo {
	if x != nil {
		return x.Contents
	}
	return nil
}

func (x *UmrahDoaRingkasInfo) GetBacaanId() uint64 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *UmrahDoaRingkasInfo) GetIsCollected() bool {
	if x != nil {
		return x.IsCollected
	}
	return false
}

// 副朝祈祷文简要列表数据
type UmrahDoaRingkasListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*UmrahDoaRingkasInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"祈祷文简要列表"` // 祈祷文简要列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaRingkasListData) Reset() {
	*x = UmrahDoaRingkasListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaRingkasListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaRingkasListData) ProtoMessage() {}

func (x *UmrahDoaRingkasListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaRingkasListData.ProtoReflect.Descriptor instead.
func (*UmrahDoaRingkasListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{54}
}

func (x *UmrahDoaRingkasListData) GetList() []*UmrahDoaRingkasInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 副朝祈祷文简要列表响应
type UmrahDoaRingkasListRes struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error            `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UmrahDoaRingkasListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaRingkasListRes) Reset() {
	*x = UmrahDoaRingkasListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaRingkasListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaRingkasListRes) ProtoMessage() {}

func (x *UmrahDoaRingkasListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaRingkasListRes.ProtoReflect.Descriptor instead.
func (*UmrahDoaRingkasListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{55}
}

func (x *UmrahDoaRingkasListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UmrahDoaRingkasListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UmrahDoaRingkasListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UmrahDoaRingkasListRes) GetData() *UmrahDoaRingkasListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 副朝祈祷文详细列表请求
type UmrahDoaPanjangListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaPanjangListReq) Reset() {
	*x = UmrahDoaPanjangListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaPanjangListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaPanjangListReq) ProtoMessage() {}

func (x *UmrahDoaPanjangListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaPanjangListReq.ProtoReflect.Descriptor instead.
func (*UmrahDoaPanjangListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{56}
}

// 副朝祈祷文详细信息
type UmrahDoaPanjangInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"祈祷文ID"`                                    // 祈祷文ID
	DoaNo         int32                  `protobuf:"varint,2,opt,name=doa_no,json=doaNo,proto3" json:"doa_no,omitempty" dc:"祈祷文序号"`                 // 祈祷文序号
	DoaName       string                 `protobuf:"bytes,3,opt,name=doa_name,json=doaName,proto3" json:"doa_name,omitempty" dc:"祈祷文名称"`            // 祈祷文名称
	BacaanCount   int32                  `protobuf:"varint,4,opt,name=bacaan_count,json=bacaanCount,proto3" json:"bacaan_count,omitempty" dc:"诵读数"` // 诵读数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaPanjangInfo) Reset() {
	*x = UmrahDoaPanjangInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaPanjangInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaPanjangInfo) ProtoMessage() {}

func (x *UmrahDoaPanjangInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaPanjangInfo.ProtoReflect.Descriptor instead.
func (*UmrahDoaPanjangInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{57}
}

func (x *UmrahDoaPanjangInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UmrahDoaPanjangInfo) GetDoaNo() int32 {
	if x != nil {
		return x.DoaNo
	}
	return 0
}

func (x *UmrahDoaPanjangInfo) GetDoaName() string {
	if x != nil {
		return x.DoaName
	}
	return ""
}

func (x *UmrahDoaPanjangInfo) GetBacaanCount() int32 {
	if x != nil {
		return x.BacaanCount
	}
	return 0
}

// 副朝祈祷文详细列表数据
type UmrahDoaPanjangListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*UmrahDoaPanjangInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"祈祷文详细列表"` // 祈祷文详细列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaPanjangListData) Reset() {
	*x = UmrahDoaPanjangListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaPanjangListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaPanjangListData) ProtoMessage() {}

func (x *UmrahDoaPanjangListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaPanjangListData.ProtoReflect.Descriptor instead.
func (*UmrahDoaPanjangListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{58}
}

func (x *UmrahDoaPanjangListData) GetList() []*UmrahDoaPanjangInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 副朝祈祷文详细列表响应
type UmrahDoaPanjangListRes struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error            `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UmrahDoaPanjangListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaPanjangListRes) Reset() {
	*x = UmrahDoaPanjangListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaPanjangListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaPanjangListRes) ProtoMessage() {}

func (x *UmrahDoaPanjangListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaPanjangListRes.ProtoReflect.Descriptor instead.
func (*UmrahDoaPanjangListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{59}
}

func (x *UmrahDoaPanjangListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UmrahDoaPanjangListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UmrahDoaPanjangListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UmrahDoaPanjangListRes) GetData() *UmrahDoaPanjangListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 副朝祈祷文诵读内容请求
type UmrahDoaPanjangBacaanListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DoaId         uint64                 `protobuf:"varint,1,opt,name=doa_id,json=doaId,proto3" json:"doa_id,omitempty" dc:"祈祷文ID"` // 祈祷文ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaPanjangBacaanListReq) Reset() {
	*x = UmrahDoaPanjangBacaanListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaPanjangBacaanListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaPanjangBacaanListReq) ProtoMessage() {}

func (x *UmrahDoaPanjangBacaanListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaPanjangBacaanListReq.ProtoReflect.Descriptor instead.
func (*UmrahDoaPanjangBacaanListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{60}
}

func (x *UmrahDoaPanjangBacaanListReq) GetDoaId() uint64 {
	if x != nil {
		return x.DoaId
	}
	return 0
}

// 副朝祈祷文诵读内容信息
type UmrahDoaPanjangBacaanContentInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"内容ID"`                                                                // 内容ID
	ContentOrder   int32                  `protobuf:"varint,2,opt,name=content_order,json=contentOrder,proto3" json:"content_order,omitempty" dc:"内容排序"`                        // 内容排序
	Title          string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"内容标题（可为空）"`                                                      // 内容标题（可为空）
	MuqattaAt      string                 `protobuf:"bytes,4,opt,name=muqatta_at,json=muqattaAt,proto3" json:"muqatta_at,omitempty" dc:"Muqattaʿāt断章字母（有则展示，无不展示）"`             // Muqattaʿāt断章字母（有则展示，无不展示）
	ArabicText     string                 `protobuf:"bytes,5,opt,name=arabic_text,json=arabicText,proto3" json:"arabic_text,omitempty" dc:"阿拉伯文原文"`                             // 阿拉伯文原文
	IndonesianText string                 `protobuf:"bytes,6,opt,name=indonesian_text,json=indonesianText,proto3" json:"indonesian_text,omitempty" dc:"印尼语翻译"`                  // 印尼语翻译
	LatinText      string                 `protobuf:"bytes,7,opt,name=latin_text,json=latinText,proto3" json:"latin_text,omitempty" dc:"拉丁音译文本"`                                // 拉丁音译文本
	ContentType    int32                  `protobuf:"varint,8,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty" dc:"内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级"` // 内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UmrahDoaPanjangBacaanContentInfo) Reset() {
	*x = UmrahDoaPanjangBacaanContentInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaPanjangBacaanContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaPanjangBacaanContentInfo) ProtoMessage() {}

func (x *UmrahDoaPanjangBacaanContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaPanjangBacaanContentInfo.ProtoReflect.Descriptor instead.
func (*UmrahDoaPanjangBacaanContentInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{61}
}

func (x *UmrahDoaPanjangBacaanContentInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UmrahDoaPanjangBacaanContentInfo) GetContentOrder() int32 {
	if x != nil {
		return x.ContentOrder
	}
	return 0
}

func (x *UmrahDoaPanjangBacaanContentInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UmrahDoaPanjangBacaanContentInfo) GetMuqattaAt() string {
	if x != nil {
		return x.MuqattaAt
	}
	return ""
}

func (x *UmrahDoaPanjangBacaanContentInfo) GetArabicText() string {
	if x != nil {
		return x.ArabicText
	}
	return ""
}

func (x *UmrahDoaPanjangBacaanContentInfo) GetIndonesianText() string {
	if x != nil {
		return x.IndonesianText
	}
	return ""
}

func (x *UmrahDoaPanjangBacaanContentInfo) GetLatinText() string {
	if x != nil {
		return x.LatinText
	}
	return ""
}

func (x *UmrahDoaPanjangBacaanContentInfo) GetContentType() int32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

// 副朝祈祷文诵读信息
type UmrahDoaPanjangBacaanInfo struct {
	state         protoimpl.MessageState              `protogen:"open.v1"`
	Id            uint64                              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"诵读ID"`                                                           // 诵读ID
	DoaId         uint64                              `protobuf:"varint,2,opt,name=doa_id,json=doaId,proto3" json:"doa_id,omitempty" dc:"祈祷文ID"`                                       // 祈祷文ID
	BacaanNo      int32                               `protobuf:"varint,3,opt,name=bacaan_no,json=bacaanNo,proto3" json:"bacaan_no,omitempty" dc:"诵读序号"`                               // 诵读序号
	BacaanName    string                              `protobuf:"bytes,4,opt,name=bacaan_name,json=bacaanName,proto3" json:"bacaan_name,omitempty" dc:"诵读名称"`                          // 诵读名称
	Contents      []*UmrahDoaPanjangBacaanContentInfo `protobuf:"bytes,5,rep,name=contents,proto3" json:"contents,omitempty" dc:"诵读内容列表"`                                              // 诵读内容列表
	BacaanId      uint64                              `protobuf:"varint,6,opt,name=bacaan_id,json=bacaanId,proto3" json:"bacaan_id,omitempty" dc:"诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)"` // 诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)
	IsCollected   bool                                `protobuf:"varint,7,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty" dc:"是否已收藏"`                     // 是否已收藏
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaPanjangBacaanInfo) Reset() {
	*x = UmrahDoaPanjangBacaanInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaPanjangBacaanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaPanjangBacaanInfo) ProtoMessage() {}

func (x *UmrahDoaPanjangBacaanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaPanjangBacaanInfo.ProtoReflect.Descriptor instead.
func (*UmrahDoaPanjangBacaanInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{62}
}

func (x *UmrahDoaPanjangBacaanInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UmrahDoaPanjangBacaanInfo) GetDoaId() uint64 {
	if x != nil {
		return x.DoaId
	}
	return 0
}

func (x *UmrahDoaPanjangBacaanInfo) GetBacaanNo() int32 {
	if x != nil {
		return x.BacaanNo
	}
	return 0
}

func (x *UmrahDoaPanjangBacaanInfo) GetBacaanName() string {
	if x != nil {
		return x.BacaanName
	}
	return ""
}

func (x *UmrahDoaPanjangBacaanInfo) GetContents() []*UmrahDoaPanjangBacaanContentInfo {
	if x != nil {
		return x.Contents
	}
	return nil
}

func (x *UmrahDoaPanjangBacaanInfo) GetBacaanId() uint64 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *UmrahDoaPanjangBacaanInfo) GetIsCollected() bool {
	if x != nil {
		return x.IsCollected
	}
	return false
}

// 副朝祈祷文诵读内容列表数据
type UmrahDoaPanjangBacaanListData struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	List          []*UmrahDoaPanjangBacaanInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"诵读内容列表"`                       // 诵读内容列表
	DoaNo         int32                        `protobuf:"varint,2,opt,name=doa_no,json=doaNo,proto3" json:"doa_no,omitempty" dc:"所属祈祷文序号"`      // 所属祈祷文序号
	DoaName       string                       `protobuf:"bytes,3,opt,name=doa_name,json=doaName,proto3" json:"doa_name,omitempty" dc:"所属祈祷文名称"` // 所属祈祷文名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaPanjangBacaanListData) Reset() {
	*x = UmrahDoaPanjangBacaanListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaPanjangBacaanListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaPanjangBacaanListData) ProtoMessage() {}

func (x *UmrahDoaPanjangBacaanListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaPanjangBacaanListData.ProtoReflect.Descriptor instead.
func (*UmrahDoaPanjangBacaanListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{63}
}

func (x *UmrahDoaPanjangBacaanListData) GetList() []*UmrahDoaPanjangBacaanInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *UmrahDoaPanjangBacaanListData) GetDoaNo() int32 {
	if x != nil {
		return x.DoaNo
	}
	return 0
}

func (x *UmrahDoaPanjangBacaanListData) GetDoaName() string {
	if x != nil {
		return x.DoaName
	}
	return ""
}

// 副朝祈祷文诵读内容列表响应
type UmrahDoaPanjangBacaanListRes struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Code          int32                          `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                         `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error                  `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UmrahDoaPanjangBacaanListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahDoaPanjangBacaanListRes) Reset() {
	*x = UmrahDoaPanjangBacaanListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahDoaPanjangBacaanListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahDoaPanjangBacaanListRes) ProtoMessage() {}

func (x *UmrahDoaPanjangBacaanListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahDoaPanjangBacaanListRes.ProtoReflect.Descriptor instead.
func (*UmrahDoaPanjangBacaanListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{64}
}

func (x *UmrahDoaPanjangBacaanListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UmrahDoaPanjangBacaanListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UmrahDoaPanjangBacaanListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UmrahDoaPanjangBacaanListRes) GetData() *UmrahDoaPanjangBacaanListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 朝觐智慧列表请求
type HajiHikmahListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiHikmahListReq) Reset() {
	*x = HajiHikmahListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiHikmahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiHikmahListReq) ProtoMessage() {}

func (x *HajiHikmahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiHikmahListReq.ProtoReflect.Descriptor instead.
func (*HajiHikmahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{65}
}

// 朝觐智慧信息
type HajiHikmahInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"朝觐智慧ID"`                              // 朝觐智慧ID
	ArticleId     uint64                 `protobuf:"varint,2,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty" dc:"文章ID"` // 文章ID
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"标题"`                             // 标题
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiHikmahInfo) Reset() {
	*x = HajiHikmahInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiHikmahInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiHikmahInfo) ProtoMessage() {}

func (x *HajiHikmahInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiHikmahInfo.ProtoReflect.Descriptor instead.
func (*HajiHikmahInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{66}
}

func (x *HajiHikmahInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiHikmahInfo) GetArticleId() uint64 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *HajiHikmahInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

// 朝觐智慧列表数据
type HajiHikmahListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*HajiHikmahInfo      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"朝觐智慧列表"` // 朝觐智慧列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiHikmahListData) Reset() {
	*x = HajiHikmahListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiHikmahListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiHikmahListData) ProtoMessage() {}

func (x *HajiHikmahListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiHikmahListData.ProtoReflect.Descriptor instead.
func (*HajiHikmahListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{67}
}

func (x *HajiHikmahListData) GetList() []*HajiHikmahInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 朝觐智慧列表响应
type HajiHikmahListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HajiHikmahListData    `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiHikmahListRes) Reset() {
	*x = HajiHikmahListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiHikmahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiHikmahListRes) ProtoMessage() {}

func (x *HajiHikmahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiHikmahListRes.ProtoReflect.Descriptor instead.
func (*HajiHikmahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{68}
}

func (x *HajiHikmahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HajiHikmahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HajiHikmahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HajiHikmahListRes) GetData() *HajiHikmahListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 副朝智慧列表请求
type UmrahHikmahListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahHikmahListReq) Reset() {
	*x = UmrahHikmahListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahHikmahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahHikmahListReq) ProtoMessage() {}

func (x *UmrahHikmahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahHikmahListReq.ProtoReflect.Descriptor instead.
func (*UmrahHikmahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{69}
}

// 副朝智慧信息
type UmrahHikmahInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"副朝智慧ID"`                              // 副朝智慧ID
	ArticleId     uint64                 `protobuf:"varint,2,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty" dc:"文章ID"` // 文章ID
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"标题"`                             // 标题
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahHikmahInfo) Reset() {
	*x = UmrahHikmahInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahHikmahInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahHikmahInfo) ProtoMessage() {}

func (x *UmrahHikmahInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahHikmahInfo.ProtoReflect.Descriptor instead.
func (*UmrahHikmahInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{70}
}

func (x *UmrahHikmahInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UmrahHikmahInfo) GetArticleId() uint64 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *UmrahHikmahInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

// 副朝智慧列表数据
type UmrahHikmahListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*UmrahHikmahInfo     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"副朝智慧列表"` // 副朝智慧列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahHikmahListData) Reset() {
	*x = UmrahHikmahListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahHikmahListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahHikmahListData) ProtoMessage() {}

func (x *UmrahHikmahListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahHikmahListData.ProtoReflect.Descriptor instead.
func (*UmrahHikmahListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{71}
}

func (x *UmrahHikmahListData) GetList() []*UmrahHikmahInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 副朝智慧列表响应
type UmrahHikmahListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UmrahHikmahListData   `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahHikmahListRes) Reset() {
	*x = UmrahHikmahListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahHikmahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahHikmahListRes) ProtoMessage() {}

func (x *UmrahHikmahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahHikmahListRes.ProtoReflect.Descriptor instead.
func (*UmrahHikmahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{72}
}

func (x *UmrahHikmahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UmrahHikmahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UmrahHikmahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UmrahHikmahListRes) GetData() *UmrahHikmahListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 朝觐地标列表请求
type HajiLandmarkListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InnerType     string                 `protobuf:"bytes,1,opt,name=inner_type,json=innerType,proto3" json:"inner_type,omitempty" dc:"内部类型: destinasi(目的地), tokoh(人物)"` // 内部类型: destinasi(目的地), tokoh(人物)
	Page          *common.PageRequest    `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                                       // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiLandmarkListReq) Reset() {
	*x = HajiLandmarkListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiLandmarkListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiLandmarkListReq) ProtoMessage() {}

func (x *HajiLandmarkListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiLandmarkListReq.ProtoReflect.Descriptor instead.
func (*HajiLandmarkListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{73}
}

func (x *HajiLandmarkListReq) GetInnerType() string {
	if x != nil {
		return x.InnerType
	}
	return ""
}

func (x *HajiLandmarkListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 朝觐地标信息（列表时返回）
type HajiLandmarkItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LandmarkId    uint64                 `protobuf:"varint,1,opt,name=landmark_id,json=landmarkId,proto3" json:"landmark_id,omitempty" dc:"地标ID"`        // 地标ID
	TypeIconUrl   string                 `protobuf:"bytes,2,opt,name=type_icon_url,json=typeIconUrl,proto3" json:"type_icon_url,omitempty" dc:"类型图标URL"` // 类型图标URL
	TypeName      string                 `protobuf:"bytes,3,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty" dc:"类型名称"`               // 类型名称
	Latitude      float64                `protobuf:"fixed64,4,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度"`                               // 纬度
	Longitude     float64                `protobuf:"fixed64,5,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度"`                             // 经度
	ImageUrl      string                 `protobuf:"bytes,6,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty" dc:"图片URL"`              // 图片URL
	LandmarkName  string                 `protobuf:"bytes,7,opt,name=landmark_name,json=landmarkName,proto3" json:"landmark_name,omitempty" dc:"地标名称"`   // 地标名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiLandmarkItem) Reset() {
	*x = HajiLandmarkItem{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiLandmarkItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiLandmarkItem) ProtoMessage() {}

func (x *HajiLandmarkItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiLandmarkItem.ProtoReflect.Descriptor instead.
func (*HajiLandmarkItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{74}
}

func (x *HajiLandmarkItem) GetLandmarkId() uint64 {
	if x != nil {
		return x.LandmarkId
	}
	return 0
}

func (x *HajiLandmarkItem) GetTypeIconUrl() string {
	if x != nil {
		return x.TypeIconUrl
	}
	return ""
}

func (x *HajiLandmarkItem) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

func (x *HajiLandmarkItem) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *HajiLandmarkItem) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *HajiLandmarkItem) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *HajiLandmarkItem) GetLandmarkName() string {
	if x != nil {
		return x.LandmarkName
	}
	return ""
}

// 朝觐地标信息（详情时返回）
type HajiLandmarkInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	LandmarkId       uint64                 `protobuf:"varint,1,opt,name=landmark_id,json=landmarkId,proto3" json:"landmark_id,omitempty" dc:"地标ID"`                 // 地标ID
	TypeIconUrl      string                 `protobuf:"bytes,2,opt,name=type_icon_url,json=typeIconUrl,proto3" json:"type_icon_url,omitempty" dc:"类型图标URL"`          // 类型图标URL
	TypeName         string                 `protobuf:"bytes,3,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty" dc:"类型名称"`                        // 类型名称
	Latitude         float64                `protobuf:"fixed64,4,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度"`                                        // 纬度
	Longitude        float64                `protobuf:"fixed64,5,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度"`                                      // 经度
	ImageUrl         string                 `protobuf:"bytes,6,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty" dc:"图片URL"`                       // 图片URL
	LandmarkName     string                 `protobuf:"bytes,7,opt,name=landmark_name,json=landmarkName,proto3" json:"landmark_name,omitempty" dc:"地标名称"`            // 地标名称
	Country          string                 `protobuf:"bytes,8,opt,name=country,proto3" json:"country,omitempty" dc:"国家/地区"`                                         // 国家/地区
	Address          string                 `protobuf:"bytes,9,opt,name=address,proto3" json:"address,omitempty" dc:"详细地址"`                                          // 详细地址
	ShortDescription string                 `protobuf:"bytes,10,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty" dc:"简介"` // 简介
	InformationText  string                 `protobuf:"bytes,11,opt,name=information_text,json=informationText,proto3" json:"information_text,omitempty" dc:"详细介绍"`  // 详细介绍
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *HajiLandmarkInfo) Reset() {
	*x = HajiLandmarkInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiLandmarkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiLandmarkInfo) ProtoMessage() {}

func (x *HajiLandmarkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiLandmarkInfo.ProtoReflect.Descriptor instead.
func (*HajiLandmarkInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{75}
}

func (x *HajiLandmarkInfo) GetLandmarkId() uint64 {
	if x != nil {
		return x.LandmarkId
	}
	return 0
}

func (x *HajiLandmarkInfo) GetTypeIconUrl() string {
	if x != nil {
		return x.TypeIconUrl
	}
	return ""
}

func (x *HajiLandmarkInfo) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

func (x *HajiLandmarkInfo) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *HajiLandmarkInfo) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *HajiLandmarkInfo) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *HajiLandmarkInfo) GetLandmarkName() string {
	if x != nil {
		return x.LandmarkName
	}
	return ""
}

func (x *HajiLandmarkInfo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *HajiLandmarkInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *HajiLandmarkInfo) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *HajiLandmarkInfo) GetInformationText() string {
	if x != nil {
		return x.InformationText
	}
	return ""
}

// 朝觐地标列表数据
type HajiLandmarkListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*HajiLandmarkItem    `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"地标列表"` // 地标列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页响应"` // 分页响应
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiLandmarkListData) Reset() {
	*x = HajiLandmarkListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiLandmarkListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiLandmarkListData) ProtoMessage() {}

func (x *HajiLandmarkListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiLandmarkListData.ProtoReflect.Descriptor instead.
func (*HajiLandmarkListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{76}
}

func (x *HajiLandmarkListData) GetList() []*HajiLandmarkItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *HajiLandmarkListData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 朝觐地标列表响应
type HajiLandmarkListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HajiLandmarkListData  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiLandmarkListRes) Reset() {
	*x = HajiLandmarkListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiLandmarkListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiLandmarkListRes) ProtoMessage() {}

func (x *HajiLandmarkListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiLandmarkListRes.ProtoReflect.Descriptor instead.
func (*HajiLandmarkListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{77}
}

func (x *HajiLandmarkListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HajiLandmarkListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HajiLandmarkListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HajiLandmarkListRes) GetData() *HajiLandmarkListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 朝觐地标详情请求
type HajiLandmarkDetailReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LandmarkId    uint64                 `protobuf:"varint,1,opt,name=landmark_id,json=landmarkId,proto3" json:"landmark_id,omitempty" dc:"地标ID"` // 地标ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiLandmarkDetailReq) Reset() {
	*x = HajiLandmarkDetailReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiLandmarkDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiLandmarkDetailReq) ProtoMessage() {}

func (x *HajiLandmarkDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiLandmarkDetailReq.ProtoReflect.Descriptor instead.
func (*HajiLandmarkDetailReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{78}
}

func (x *HajiLandmarkDetailReq) GetLandmarkId() uint64 {
	if x != nil {
		return x.LandmarkId
	}
	return 0
}

// 朝觐地标详情数据
type HajiLandmarkDetailData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Landmark      *HajiLandmarkInfo      `protobuf:"bytes,1,opt,name=landmark,proto3" json:"landmark,omitempty" dc:"地标详情信息"` // 地标详情信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiLandmarkDetailData) Reset() {
	*x = HajiLandmarkDetailData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiLandmarkDetailData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiLandmarkDetailData) ProtoMessage() {}

func (x *HajiLandmarkDetailData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiLandmarkDetailData.ProtoReflect.Descriptor instead.
func (*HajiLandmarkDetailData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{79}
}

func (x *HajiLandmarkDetailData) GetLandmark() *HajiLandmarkInfo {
	if x != nil {
		return x.Landmark
	}
	return nil
}

// 朝觐地标详情响应
type HajiLandmarkDetailRes struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Code          int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error           `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HajiLandmarkDetailData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiLandmarkDetailRes) Reset() {
	*x = HajiLandmarkDetailRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiLandmarkDetailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiLandmarkDetailRes) ProtoMessage() {}

func (x *HajiLandmarkDetailRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiLandmarkDetailRes.ProtoReflect.Descriptor instead.
func (*HajiLandmarkDetailRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{80}
}

func (x *HajiLandmarkDetailRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HajiLandmarkDetailRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HajiLandmarkDetailRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HajiLandmarkDetailRes) GetData() *HajiLandmarkDetailData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 副朝地标列表请求
type UmrahLandmarkListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InnerType     string                 `protobuf:"bytes,1,opt,name=inner_type,json=innerType,proto3" json:"inner_type,omitempty" dc:"内部类型: destinasi(目的地), tokoh(人物)"` // 内部类型: destinasi(目的地), tokoh(人物)
	Page          *common.PageRequest    `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                                       // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahLandmarkListReq) Reset() {
	*x = UmrahLandmarkListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahLandmarkListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahLandmarkListReq) ProtoMessage() {}

func (x *UmrahLandmarkListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahLandmarkListReq.ProtoReflect.Descriptor instead.
func (*UmrahLandmarkListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{81}
}

func (x *UmrahLandmarkListReq) GetInnerType() string {
	if x != nil {
		return x.InnerType
	}
	return ""
}

func (x *UmrahLandmarkListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 副朝地标信息（列表时返回）
type UmrahLandmarkItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LandmarkId    uint64                 `protobuf:"varint,1,opt,name=landmark_id,json=landmarkId,proto3" json:"landmark_id,omitempty" dc:"地标ID"`        // 地标ID
	TypeIconUrl   string                 `protobuf:"bytes,2,opt,name=type_icon_url,json=typeIconUrl,proto3" json:"type_icon_url,omitempty" dc:"类型图标URL"` // 类型图标URL
	TypeName      string                 `protobuf:"bytes,3,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty" dc:"类型名称"`               // 类型名称
	Latitude      float64                `protobuf:"fixed64,4,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度"`                               // 纬度
	Longitude     float64                `protobuf:"fixed64,5,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度"`                             // 经度
	ImageUrl      string                 `protobuf:"bytes,6,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty" dc:"图片URL"`              // 图片URL
	LandmarkName  string                 `protobuf:"bytes,7,opt,name=landmark_name,json=landmarkName,proto3" json:"landmark_name,omitempty" dc:"地标名称"`   // 地标名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahLandmarkItem) Reset() {
	*x = UmrahLandmarkItem{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahLandmarkItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahLandmarkItem) ProtoMessage() {}

func (x *UmrahLandmarkItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahLandmarkItem.ProtoReflect.Descriptor instead.
func (*UmrahLandmarkItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{82}
}

func (x *UmrahLandmarkItem) GetLandmarkId() uint64 {
	if x != nil {
		return x.LandmarkId
	}
	return 0
}

func (x *UmrahLandmarkItem) GetTypeIconUrl() string {
	if x != nil {
		return x.TypeIconUrl
	}
	return ""
}

func (x *UmrahLandmarkItem) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

func (x *UmrahLandmarkItem) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *UmrahLandmarkItem) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *UmrahLandmarkItem) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *UmrahLandmarkItem) GetLandmarkName() string {
	if x != nil {
		return x.LandmarkName
	}
	return ""
}

// 副朝地标信息（详情时返回）
type UmrahLandmarkInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	LandmarkId       uint64                 `protobuf:"varint,1,opt,name=landmark_id,json=landmarkId,proto3" json:"landmark_id,omitempty" dc:"地标ID"`                 // 地标ID
	TypeIconUrl      string                 `protobuf:"bytes,2,opt,name=type_icon_url,json=typeIconUrl,proto3" json:"type_icon_url,omitempty" dc:"类型图标URL"`          // 类型图标URL
	TypeName         string                 `protobuf:"bytes,3,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty" dc:"类型名称"`                        // 类型名称
	Latitude         float64                `protobuf:"fixed64,4,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度"`                                        // 纬度
	Longitude        float64                `protobuf:"fixed64,5,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度"`                                      // 经度
	ImageUrl         string                 `protobuf:"bytes,6,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty" dc:"图片URL"`                       // 图片URL
	LandmarkName     string                 `protobuf:"bytes,7,opt,name=landmark_name,json=landmarkName,proto3" json:"landmark_name,omitempty" dc:"地标名称"`            // 地标名称
	Country          string                 `protobuf:"bytes,8,opt,name=country,proto3" json:"country,omitempty" dc:"国家/地区"`                                         // 国家/地区
	Address          string                 `protobuf:"bytes,9,opt,name=address,proto3" json:"address,omitempty" dc:"详细地址"`                                          // 详细地址
	ShortDescription string                 `protobuf:"bytes,10,opt,name=short_description,json=shortDescription,proto3" json:"short_description,omitempty" dc:"简介"` // 简介
	InformationText  string                 `protobuf:"bytes,11,opt,name=information_text,json=informationText,proto3" json:"information_text,omitempty" dc:"详细介绍"`  // 详细介绍
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UmrahLandmarkInfo) Reset() {
	*x = UmrahLandmarkInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[83]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahLandmarkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahLandmarkInfo) ProtoMessage() {}

func (x *UmrahLandmarkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[83]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahLandmarkInfo.ProtoReflect.Descriptor instead.
func (*UmrahLandmarkInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{83}
}

func (x *UmrahLandmarkInfo) GetLandmarkId() uint64 {
	if x != nil {
		return x.LandmarkId
	}
	return 0
}

func (x *UmrahLandmarkInfo) GetTypeIconUrl() string {
	if x != nil {
		return x.TypeIconUrl
	}
	return ""
}

func (x *UmrahLandmarkInfo) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

func (x *UmrahLandmarkInfo) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *UmrahLandmarkInfo) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *UmrahLandmarkInfo) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *UmrahLandmarkInfo) GetLandmarkName() string {
	if x != nil {
		return x.LandmarkName
	}
	return ""
}

func (x *UmrahLandmarkInfo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *UmrahLandmarkInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UmrahLandmarkInfo) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *UmrahLandmarkInfo) GetInformationText() string {
	if x != nil {
		return x.InformationText
	}
	return ""
}

// 副朝地标列表数据
type UmrahLandmarkListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*UmrahLandmarkItem   `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"地标列表"` // 地标列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页响应"` // 分页响应
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahLandmarkListData) Reset() {
	*x = UmrahLandmarkListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[84]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahLandmarkListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahLandmarkListData) ProtoMessage() {}

func (x *UmrahLandmarkListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[84]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahLandmarkListData.ProtoReflect.Descriptor instead.
func (*UmrahLandmarkListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{84}
}

func (x *UmrahLandmarkListData) GetList() []*UmrahLandmarkItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *UmrahLandmarkListData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 副朝地标列表响应
type UmrahLandmarkListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UmrahLandmarkListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahLandmarkListRes) Reset() {
	*x = UmrahLandmarkListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[85]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahLandmarkListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahLandmarkListRes) ProtoMessage() {}

func (x *UmrahLandmarkListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[85]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahLandmarkListRes.ProtoReflect.Descriptor instead.
func (*UmrahLandmarkListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{85}
}

func (x *UmrahLandmarkListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UmrahLandmarkListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UmrahLandmarkListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UmrahLandmarkListRes) GetData() *UmrahLandmarkListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 副朝地标详情请求
type UmrahLandmarkDetailReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LandmarkId    uint64                 `protobuf:"varint,1,opt,name=landmark_id,json=landmarkId,proto3" json:"landmark_id,omitempty" dc:"地标ID"` // 地标ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahLandmarkDetailReq) Reset() {
	*x = UmrahLandmarkDetailReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[86]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahLandmarkDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahLandmarkDetailReq) ProtoMessage() {}

func (x *UmrahLandmarkDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[86]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahLandmarkDetailReq.ProtoReflect.Descriptor instead.
func (*UmrahLandmarkDetailReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{86}
}

func (x *UmrahLandmarkDetailReq) GetLandmarkId() uint64 {
	if x != nil {
		return x.LandmarkId
	}
	return 0
}

// 副朝地标详情数据
type UmrahLandmarkDetailData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Landmark      *UmrahLandmarkInfo     `protobuf:"bytes,1,opt,name=landmark,proto3" json:"landmark,omitempty" dc:"地标详情信息"` // 地标详情信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahLandmarkDetailData) Reset() {
	*x = UmrahLandmarkDetailData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[87]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahLandmarkDetailData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahLandmarkDetailData) ProtoMessage() {}

func (x *UmrahLandmarkDetailData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[87]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahLandmarkDetailData.ProtoReflect.Descriptor instead.
func (*UmrahLandmarkDetailData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{87}
}

func (x *UmrahLandmarkDetailData) GetLandmark() *UmrahLandmarkInfo {
	if x != nil {
		return x.Landmark
	}
	return nil
}

// 副朝地标详情响应
type UmrahLandmarkDetailRes struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error            `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UmrahLandmarkDetailData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahLandmarkDetailRes) Reset() {
	*x = UmrahLandmarkDetailRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[88]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahLandmarkDetailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahLandmarkDetailRes) ProtoMessage() {}

func (x *UmrahLandmarkDetailRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[88]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahLandmarkDetailRes.ProtoReflect.Descriptor instead.
func (*UmrahLandmarkDetailRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{88}
}

func (x *UmrahLandmarkDetailRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UmrahLandmarkDetailRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UmrahLandmarkDetailRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UmrahLandmarkDetailRes) GetData() *UmrahLandmarkDetailData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 朝觐新闻列表请求
type HajiNewsListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiNewsListReq) Reset() {
	*x = HajiNewsListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[89]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiNewsListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiNewsListReq) ProtoMessage() {}

func (x *HajiNewsListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[89]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiNewsListReq.ProtoReflect.Descriptor instead.
func (*HajiNewsListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{89}
}

func (x *HajiNewsListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 新闻列表项
type HajiNewsItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ArticleId     uint64                 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty" dc:"文章ID"`              // 文章ID
	CategoryName  string                 `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty" dc:"资讯分类名称"`    // 资讯分类名称
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"标题"`                                          // 标题
	PublishTime   int64                  `protobuf:"varint,4,opt,name=publish_time,json=publishTime,proto3" json:"publish_time,omitempty" dc:"发布时间（毫秒时间戳）"` // 发布时间（毫秒时间戳）
	CoverImage    string                 `protobuf:"bytes,5,opt,name=cover_image,json=coverImage,proto3" json:"cover_image,omitempty" dc:"封面图片URL"`         // 封面图片URL
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiNewsItem) Reset() {
	*x = HajiNewsItem{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[90]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiNewsItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiNewsItem) ProtoMessage() {}

func (x *HajiNewsItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[90]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiNewsItem.ProtoReflect.Descriptor instead.
func (*HajiNewsItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{90}
}

func (x *HajiNewsItem) GetArticleId() uint64 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *HajiNewsItem) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *HajiNewsItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *HajiNewsItem) GetPublishTime() int64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

func (x *HajiNewsItem) GetCoverImage() string {
	if x != nil {
		return x.CoverImage
	}
	return ""
}

// 朝觐新闻列表数据
type HajiNewsListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*HajiNewsItem        `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"新闻列表"`                                          // 新闻列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`                                          // 分页信息
	HeaderTag     string                 `protobuf:"bytes,3,opt,name=header_tag,json=headerTag,proto3" json:"header_tag,omitempty" dc:"sort_order 最靠前的标签名"` // sort_order 最靠前的标签名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiNewsListData) Reset() {
	*x = HajiNewsListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[91]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiNewsListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiNewsListData) ProtoMessage() {}

func (x *HajiNewsListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[91]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiNewsListData.ProtoReflect.Descriptor instead.
func (*HajiNewsListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{91}
}

func (x *HajiNewsListData) GetList() []*HajiNewsItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *HajiNewsListData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *HajiNewsListData) GetHeaderTag() string {
	if x != nil {
		return x.HeaderTag
	}
	return ""
}

// 朝觐新闻列表响应
type HajiNewsListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *HajiNewsListData      `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiNewsListRes) Reset() {
	*x = HajiNewsListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[92]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiNewsListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiNewsListRes) ProtoMessage() {}

func (x *HajiNewsListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[92]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiNewsListRes.ProtoReflect.Descriptor instead.
func (*HajiNewsListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{92}
}

func (x *HajiNewsListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HajiNewsListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HajiNewsListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *HajiNewsListRes) GetData() *HajiNewsListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 斋月祈祷文简要列表请求
type RamadhanDoaListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RamadhanDoaListReq) Reset() {
	*x = RamadhanDoaListReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[93]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RamadhanDoaListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RamadhanDoaListReq) ProtoMessage() {}

func (x *RamadhanDoaListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[93]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RamadhanDoaListReq.ProtoReflect.Descriptor instead.
func (*RamadhanDoaListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{93}
}

// 斋月祈祷文内容信息
type RamadhanDoaContentInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"内容ID"`                                                                // 内容ID
	ContentOrder   int32                  `protobuf:"varint,2,opt,name=content_order,json=contentOrder,proto3" json:"content_order,omitempty" dc:"内容排序"`                        // 内容排序
	Title          string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"内容标题（可为空）"`                                                      // 内容标题（可为空）
	MuqattaAt      string                 `protobuf:"bytes,4,opt,name=muqatta_at,json=muqattaAt,proto3" json:"muqatta_at,omitempty" dc:"Muqattaʿāt断章字母（有则展示，无不展示）"`             // Muqattaʿāt断章字母（有则展示，无不展示）
	ArabicText     string                 `protobuf:"bytes,5,opt,name=arabic_text,json=arabicText,proto3" json:"arabic_text,omitempty" dc:"阿拉伯文原文"`                             // 阿拉伯文原文
	IndonesianText string                 `protobuf:"bytes,6,opt,name=indonesian_text,json=indonesianText,proto3" json:"indonesian_text,omitempty" dc:"印尼语翻译"`                  // 印尼语翻译
	LatinText      string                 `protobuf:"bytes,7,opt,name=latin_text,json=latinText,proto3" json:"latin_text,omitempty" dc:"拉丁音译文本"`                                // 拉丁音译文本
	ContentType    int32                  `protobuf:"varint,8,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty" dc:"内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级"` // 内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RamadhanDoaContentInfo) Reset() {
	*x = RamadhanDoaContentInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[94]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RamadhanDoaContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RamadhanDoaContentInfo) ProtoMessage() {}

func (x *RamadhanDoaContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[94]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RamadhanDoaContentInfo.ProtoReflect.Descriptor instead.
func (*RamadhanDoaContentInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{94}
}

func (x *RamadhanDoaContentInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RamadhanDoaContentInfo) GetContentOrder() int32 {
	if x != nil {
		return x.ContentOrder
	}
	return 0
}

func (x *RamadhanDoaContentInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RamadhanDoaContentInfo) GetMuqattaAt() string {
	if x != nil {
		return x.MuqattaAt
	}
	return ""
}

func (x *RamadhanDoaContentInfo) GetArabicText() string {
	if x != nil {
		return x.ArabicText
	}
	return ""
}

func (x *RamadhanDoaContentInfo) GetIndonesianText() string {
	if x != nil {
		return x.IndonesianText
	}
	return ""
}

func (x *RamadhanDoaContentInfo) GetLatinText() string {
	if x != nil {
		return x.LatinText
	}
	return ""
}

func (x *RamadhanDoaContentInfo) GetContentType() int32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

// 斋月祈祷文简要信息
type RamadhanDoaInfo struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Id            uint64                    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"祈祷文ID"`                                                          // 祈祷文ID
	DoaNo         int32                     `protobuf:"varint,2,opt,name=doa_no,json=doaNo,proto3" json:"doa_no,omitempty" dc:"祈祷文序号"`                                       // 祈祷文序号
	DoaName       string                    `protobuf:"bytes,3,opt,name=doa_name,json=doaName,proto3" json:"doa_name,omitempty" dc:"祈祷文名称"`                                  // 祈祷文名称
	Contents      []*RamadhanDoaContentInfo `protobuf:"bytes,4,rep,name=contents,proto3" json:"contents,omitempty" dc:"祈祷文内容列表"`                                             // 祈祷文内容列表
	BacaanId      uint64                    `protobuf:"varint,5,opt,name=bacaan_id,json=bacaanId,proto3" json:"bacaan_id,omitempty" dc:"诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)"` // 诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)
	IsCollected   bool                      `protobuf:"varint,6,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty" dc:"是否已收藏"`                     // 是否已收藏
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RamadhanDoaInfo) Reset() {
	*x = RamadhanDoaInfo{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[95]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RamadhanDoaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RamadhanDoaInfo) ProtoMessage() {}

func (x *RamadhanDoaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[95]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RamadhanDoaInfo.ProtoReflect.Descriptor instead.
func (*RamadhanDoaInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{95}
}

func (x *RamadhanDoaInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RamadhanDoaInfo) GetDoaNo() int32 {
	if x != nil {
		return x.DoaNo
	}
	return 0
}

func (x *RamadhanDoaInfo) GetDoaName() string {
	if x != nil {
		return x.DoaName
	}
	return ""
}

func (x *RamadhanDoaInfo) GetContents() []*RamadhanDoaContentInfo {
	if x != nil {
		return x.Contents
	}
	return nil
}

func (x *RamadhanDoaInfo) GetBacaanId() uint64 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *RamadhanDoaInfo) GetIsCollected() bool {
	if x != nil {
		return x.IsCollected
	}
	return false
}

// 斋月祈祷文简要列表数据
type RamadhanDoaListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*RamadhanDoaInfo     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"祈祷文简要列表"` // 祈祷文简要列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RamadhanDoaListData) Reset() {
	*x = RamadhanDoaListData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[96]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RamadhanDoaListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RamadhanDoaListData) ProtoMessage() {}

func (x *RamadhanDoaListData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[96]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RamadhanDoaListData.ProtoReflect.Descriptor instead.
func (*RamadhanDoaListData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{96}
}

func (x *RamadhanDoaListData) GetList() []*RamadhanDoaInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 斋月祈祷文简要列表响应
type RamadhanDoaListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *RamadhanDoaListData   `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RamadhanDoaListRes) Reset() {
	*x = RamadhanDoaListRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[97]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RamadhanDoaListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RamadhanDoaListRes) ProtoMessage() {}

func (x *RamadhanDoaListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[97]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RamadhanDoaListRes.ProtoReflect.Descriptor instead.
func (*RamadhanDoaListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{97}
}

func (x *RamadhanDoaListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RamadhanDoaListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *RamadhanDoaListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *RamadhanDoaListRes) GetData() *RamadhanDoaListData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 所有doa的详情都可以使用这个接口
type CommonDoaBacaanDetailReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BacaanId      uint64                 `protobuf:"varint,1,opt,name=bacaan_id,json=bacaanId,proto3" json:"bacaan_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonDoaBacaanDetailReq) Reset() {
	*x = CommonDoaBacaanDetailReq{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[98]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonDoaBacaanDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonDoaBacaanDetailReq) ProtoMessage() {}

func (x *CommonDoaBacaanDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[98]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonDoaBacaanDetailReq.ProtoReflect.Descriptor instead.
func (*CommonDoaBacaanDetailReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{98}
}

func (x *CommonDoaBacaanDetailReq) GetBacaanId() uint64 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

type CommonDoaBacaanContent struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ArabicText     string                 `protobuf:"bytes,1,opt,name=arabic_text,json=arabicText,proto3" json:"arabic_text,omitempty" dc:"阿拉伯文原文"`                             // 阿拉伯文原文
	IndonesianText string                 `protobuf:"bytes,2,opt,name=indonesian_text,json=indonesianText,proto3" json:"indonesian_text,omitempty" dc:"印尼语翻译"`                  // 印尼语翻译
	LatinText      string                 `protobuf:"bytes,3,opt,name=latin_text,json=latinText,proto3" json:"latin_text,omitempty" dc:"拉丁音译文本"`                                // 拉丁音译文本
	ContentType    int32                  `protobuf:"varint,4,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty" dc:"内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级"` // 内容类型：1/2/3，分别对应3种类型的卡片，也对应3个层级
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CommonDoaBacaanContent) Reset() {
	*x = CommonDoaBacaanContent{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[99]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonDoaBacaanContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonDoaBacaanContent) ProtoMessage() {}

func (x *CommonDoaBacaanContent) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[99]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonDoaBacaanContent.ProtoReflect.Descriptor instead.
func (*CommonDoaBacaanContent) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{99}
}

func (x *CommonDoaBacaanContent) GetArabicText() string {
	if x != nil {
		return x.ArabicText
	}
	return ""
}

func (x *CommonDoaBacaanContent) GetIndonesianText() string {
	if x != nil {
		return x.IndonesianText
	}
	return ""
}

func (x *CommonDoaBacaanContent) GetLatinText() string {
	if x != nil {
		return x.LatinText
	}
	return ""
}

func (x *CommonDoaBacaanContent) GetContentType() int32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

type CommonDoaBacaanDetailData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*CommonDoaBacaanData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"诵读列表"` // 诵读列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonDoaBacaanDetailData) Reset() {
	*x = CommonDoaBacaanDetailData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[100]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonDoaBacaanDetailData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonDoaBacaanDetailData) ProtoMessage() {}

func (x *CommonDoaBacaanDetailData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[100]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonDoaBacaanDetailData.ProtoReflect.Descriptor instead.
func (*CommonDoaBacaanDetailData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{100}
}

func (x *CommonDoaBacaanDetailData) GetList() []*CommonDoaBacaanData {
	if x != nil {
		return x.List
	}
	return nil
}

type CommonDoaBacaanData struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	BacaanId      uint64                    `protobuf:"varint,1,opt,name=bacaan_id,json=bacaanId,proto3" json:"bacaan_id,omitempty" dc:"诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)"` // 诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)
	BacaanNo      int32                     `protobuf:"varint,2,opt,name=bacaan_no,json=bacaanNo,proto3" json:"bacaan_no,omitempty" dc:"诵读序号"`                               // 诵读序号
	BacaanName    string                    `protobuf:"bytes,3,opt,name=bacaan_name,json=bacaanName,proto3" json:"bacaan_name,omitempty" dc:"诵读名称"`                          // 诵读名称
	IsCollected   bool                      `protobuf:"varint,4,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty" dc:"是否已收藏"`                     // 是否已收藏
	Contents      []*CommonDoaBacaanContent `protobuf:"bytes,5,rep,name=contents,proto3" json:"contents,omitempty" dc:"诵读内容列表"`                                              // 诵读内容列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonDoaBacaanData) Reset() {
	*x = CommonDoaBacaanData{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[101]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonDoaBacaanData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonDoaBacaanData) ProtoMessage() {}

func (x *CommonDoaBacaanData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[101]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonDoaBacaanData.ProtoReflect.Descriptor instead.
func (*CommonDoaBacaanData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{101}
}

func (x *CommonDoaBacaanData) GetBacaanId() uint64 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *CommonDoaBacaanData) GetBacaanNo() int32 {
	if x != nil {
		return x.BacaanNo
	}
	return 0
}

func (x *CommonDoaBacaanData) GetBacaanName() string {
	if x != nil {
		return x.BacaanName
	}
	return ""
}

func (x *CommonDoaBacaanData) GetIsCollected() bool {
	if x != nil {
		return x.IsCollected
	}
	return false
}

func (x *CommonDoaBacaanData) GetContents() []*CommonDoaBacaanContent {
	if x != nil {
		return x.Contents
	}
	return nil
}

type CommonDoaBacaanDetailRes struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Code          int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error              `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *CommonDoaBacaanDetailData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonDoaBacaanDetailRes) Reset() {
	*x = CommonDoaBacaanDetailRes{}
	mi := &file_islamic_v1_prayer_proto_msgTypes[102]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonDoaBacaanDetailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonDoaBacaanDetailRes) ProtoMessage() {}

func (x *CommonDoaBacaanDetailRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[102]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonDoaBacaanDetailRes.ProtoReflect.Descriptor instead.
func (*CommonDoaBacaanDetailRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{102}
}

func (x *CommonDoaBacaanDetailRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonDoaBacaanDetailRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CommonDoaBacaanDetailRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CommonDoaBacaanDetailRes) GetData() *CommonDoaBacaanDetailData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_prayer_proto protoreflect.FileDescriptor

const file_islamic_v1_prayer_proto_rawDesc = "" +
	"\n" +
	"\x17islamic/v1/prayer.proto\x12\n" +
	"islamic.v1\x1a\x11common/base.proto\x1a\x1egoogle/protobuf/wrappers.proto\"\x81\x01\n" +
	"\vCalendarReq\x12\x12\n" +
	"\x04year\x18\x01 \x01(\x05R\x04year\x12\x14\n" +
	"\x05month\x18\x02 \x01(\x05R\x05month\x12\x1f\n" +
	"\vmethod_code\x18\x03 \x01(\tR\n" +
	"methodCode\x12'\n" +
	"\x0fdate_adjustment\x18\x04 \x01(\x05R\x0edateAdjustment\"}\n" +
	"\x10BatchCalendarReq\x12\x1f\n" +
	"\vyear_months\x18\x01 \x03(\tR\n" +
	"yearMonths\x12\x1f\n" +
	"\vmethod_code\x18\x02 \x01(\tR\n" +
	"methodCode\x12'\n" +
	"\x0fdate_adjustment\x18\x03 \x01(\x05R\x0edateAdjustment\"\xc2\x03\n" +
	"\x10CalendarDateInfo\x12%\n" +
	"\x0egregorian_year\x18\x01 \x01(\x05R\rgregorianYear\x12'\n" +
	"\x0fgregorian_month\x18\x02 \x01(\x05R\x0egregorianMonth\x12#\n" +
	"\rgregorian_day\x18\x03 \x01(\x05R\fgregorianDay\x12!\n" +
	"\fhijriah_year\x18\x04 \x01(\x05R\vhijriahYear\x12#\n" +
	"\rhijriah_month\x18\x05 \x01(\x05R\fhijriahMonth\x12\x1f\n" +
	"\vhijriah_day\x18\x06 \x01(\x05R\n" +
	"hijriahDay\x12\x1f\n" +
	"\vmethod_code\x18\a \x01(\tR\n" +
	"methodCode\x12\x18\n" +
	"\aweekday\x18\b \x01(\x05R\aweekday\x12\x18\n" +
	"\apasaran\x18\t \x01(\x05R\apasaran\x12!\n" +
	"\fweekday_name\x18\n" +
	" \x01(\tR\vweekdayName\x12!\n" +
	"\fpasaran_name\x18\v \x01(\tR\vpasaranName\x125\n" +
	"\x06events\x18\f \x03(\v2\x1d.islamic.v1.CalendarEventInfoR\x06events\"\x95\x01\n" +
	"\x11CalendarEventInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"event_type\x18\x02 \x01(\tR\teventType\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x19\n" +
	"\bjump_url\x18\x05 \x01(\tR\ajumpUrl\"@\n" +
	"\fCalendarData\x120\n" +
	"\x04list\x18\x01 \x03(\v2\x1c.islamic.v1.CalendarDateInfoR\x04list\"\x86\x01\n" +
	"\vCalendarRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12,\n" +
	"\x04data\x18\x04 \x01(\v2\x18.islamic.v1.CalendarDataR\x04data\"\x90\x01\n" +
	"\x10BatchCalendarRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x121\n" +
	"\x04data\x18\x04 \x01(\v2\x1d.islamic.v1.BatchCalendarDataR\x04data\"\xb7\x01\n" +
	"\x11BatchCalendarData\x12J\n" +
	"\tcalendars\x18\x01 \x03(\v2,.islamic.v1.BatchCalendarData.CalendarsEntryR\tcalendars\x1aV\n" +
	"\x0eCalendarsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12.\n" +
	"\x05value\x18\x02 \x01(\v2\x18.islamic.v1.CalendarDataR\x05value:\x028\x01\"\xcb\x01\n" +
	"\x15GetDailyPrayerTimeReq\x12\x12\n" +
	"\x04date\x18\x01 \x01(\tR\x04date\x12\x1a\n" +
	"\blatitude\x18\x02 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x03 \x01(\x01R\tlongitude\x12\x1a\n" +
	"\btimezone\x18\x04 \x01(\tR\btimezone\x12\x1f\n" +
	"\vmethod_code\x18\x05 \x01(\tR\n" +
	"methodCode\x12'\n" +
	"\x0fdate_adjustment\x18\x06 \x01(\x05R\x0edateAdjustment\"\x92\x01\n" +
	"\x15GetDailyPrayerTimeRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12.\n" +
	"\x04data\x18\x04 \x01(\v2\x1a.islamic.v1.PrayerTimeDataR\x04data\"\x99\x01\n" +
	"\x0ePrayerTimeData\x12\x12\n" +
	"\x04date\x18\x01 \x01(\tR\x04date\x127\n" +
	"\vprayer_time\x18\x02 \x01(\v2\x16.islamic.v1.PrayerTimeR\n" +
	"prayerTime\x12:\n" +
	"\fislamic_date\x18\x03 \x01(\v2\x17.islamic.v1.IslamicDateR\vislamicDate\"\xc0\x01\n" +
	"\n" +
	"PrayerTime\x12\x14\n" +
	"\x05imsak\x18\x01 \x01(\tR\x05imsak\x12\x14\n" +
	"\x05subuh\x18\x02 \x01(\tR\x05subuh\x12\x16\n" +
	"\x06terbit\x18\x03 \x01(\tR\x06terbit\x12\x14\n" +
	"\x05dhuha\x18\x04 \x01(\tR\x05dhuha\x12\x14\n" +
	"\x05zuhur\x18\x05 \x01(\tR\x05zuhur\x12\x14\n" +
	"\x05ashar\x18\x06 \x01(\tR\x05ashar\x12\x18\n" +
	"\amaghrib\x18\a \x01(\tR\amaghrib\x12\x12\n" +
	"\x04isya\x18\b \x01(\tR\x04isya\"I\n" +
	"\vIslamicDate\x12\x12\n" +
	"\x04year\x18\x01 \x01(\x05R\x04year\x12\x14\n" +
	"\x05month\x18\x02 \x01(\x05R\x05month\x12\x10\n" +
	"\x03day\x18\x03 \x01(\x05R\x03day\"\xe4\x01\n" +
	"\x18GetMonthlyPrayerTimesReq\x12\x12\n" +
	"\x04year\x18\x01 \x01(\x05R\x04year\x12\x14\n" +
	"\x05month\x18\x02 \x01(\x05R\x05month\x12\x1a\n" +
	"\blatitude\x18\x03 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x04 \x01(\x01R\tlongitude\x12\x1a\n" +
	"\btimezone\x18\x05 \x01(\tR\btimezone\x12\x1f\n" +
	"\vmethod_code\x18\x06 \x01(\tR\n" +
	"methodCode\x12'\n" +
	"\x0fdate_adjustment\x18\a \x01(\x05R\x0edateAdjustment\"\x9d\x01\n" +
	"\x18GetMonthlyPrayerTimesRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x126\n" +
	"\x04data\x18\x04 \x01(\v2\".islamic.v1.MonthlyPrayerTimesDataR\x04data\"H\n" +
	"\x16MonthlyPrayerTimesData\x12.\n" +
	"\x04list\x18\x01 \x03(\v2\x1a.islamic.v1.PrayerTimeDataR\x04list\"\x13\n" +
	"\x11HajiJadwalListReq\"2\n" +
	"\x13HajiJadwalDetailReq\x12\x1b\n" +
	"\tjadwal_id\x18\x01 \x01(\x04R\bjadwalId\"\xc7\x01\n" +
	"\x0eHajiJadwalInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x17\n" +
	"\aitem_no\x18\x02 \x01(\x05R\x06itemNo\x12\x1b\n" +
	"\ttime_info\x18\x03 \x01(\tR\btimeInfo\x12#\n" +
	"\revent_summary\x18\x04 \x01(\tR\feventSummary\x12'\n" +
	"\x0fadditional_info\x18\x05 \x01(\tR\x0eadditionalInfo\x12!\n" +
	"\farticle_text\x18\x06 \x01(\tR\varticleText\"z\n" +
	"\x12HajiJadwalListData\x12.\n" +
	"\x04list\x18\x01 \x03(\v2\x1a.islamic.v1.HajiJadwalInfoR\x04list\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x12\n" +
	"\x04year\x18\x03 \x01(\tR\x04year\"\x92\x01\n" +
	"\x11HajiJadwalListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x122\n" +
	"\x04data\x18\x04 \x01(\v2\x1e.islamic.v1.HajiJadwalListDataR\x04data\"J\n" +
	"\x14HajiJadwalDetailData\x122\n" +
	"\x06jadwal\x18\x01 \x01(\v2\x1a.islamic.v1.HajiJadwalInfoR\x06jadwal\"\x96\x01\n" +
	"\x13HajiJadwalDetailRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x124\n" +
	"\x04data\x18\x04 \x01(\v2 .islamic.v1.HajiJadwalDetailDataR\x04data\"\x13\n" +
	"\x11HajiUrutanListReq\"2\n" +
	"\x13HajiUrutanDetailReq\x12\x1b\n" +
	"\turutan_id\x18\x01 \x01(\x04R\burutanId\"\xc1\x01\n" +
	"\x0eHajiUrutanInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1b\n" +
	"\turutan_no\x18\x02 \x01(\x05R\burutanNo\x12\x1f\n" +
	"\vurutan_name\x18\x03 \x01(\tR\n" +
	"urutanName\x12\x1f\n" +
	"\vurutan_time\x18\x04 \x01(\tR\n" +
	"urutanTime\x12\x19\n" +
	"\bicon_url\x18\x05 \x01(\tR\aiconUrl\x12%\n" +
	"\x0eurutan_content\x18\x06 \x01(\tR\rurutanContent\"D\n" +
	"\x12HajiUrutanListData\x12.\n" +
	"\x04list\x18\x01 \x03(\v2\x1a.islamic.v1.HajiUrutanInfoR\x04list\"\x92\x01\n" +
	"\x11HajiUrutanListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x122\n" +
	"\x04data\x18\x04 \x01(\v2\x1e.islamic.v1.HajiUrutanListDataR\x04data\"J\n" +
	"\x14HajiUrutanDetailData\x122\n" +
	"\x06urutan\x18\x01 \x01(\v2\x1a.islamic.v1.HajiUrutanInfoR\x06urutan\"\x96\x01\n" +
	"\x13HajiUrutanDetailRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x124\n" +
	"\x04data\x18\x04 \x01(\v2 .islamic.v1.HajiUrutanDetailDataR\x04data\"\x14\n" +
	"\x12UmrahUrutanListReq\"3\n" +
	"\x14UmrahUrutanDetailReq\x12\x1b\n" +
	"\turutan_id\x18\x01 \x01(\x04R\burutanId\"\xc2\x01\n" +
	"\x0fUmrahUrutanInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1b\n" +
	"\turutan_no\x18\x02 \x01(\x05R\burutanNo\x12\x1f\n" +
	"\vurutan_name\x18\x03 \x01(\tR\n" +
	"urutanName\x12\x1f\n" +
	"\vurutan_time\x18\x04 \x01(\tR\n" +
	"urutanTime\x12\x19\n" +
	"\bicon_url\x18\x05 \x01(\tR\aiconUrl\x12%\n" +
	"\x0eurutan_content\x18\x06 \x01(\tR\rurutanContent\"F\n" +
	"\x13UmrahUrutanListData\x12/\n" +
	"\x04list\x18\x01 \x03(\v2\x1b.islamic.v1.UmrahUrutanInfoR\x04list\"\x94\x01\n" +
	"\x12UmrahUrutanListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x123\n" +
	"\x04data\x18\x04 \x01(\v2\x1f.islamic.v1.UmrahUrutanListDataR\x04data\"L\n" +
	"\x15UmrahUrutanDetailData\x123\n" +
	"\x06urutan\x18\x01 \x01(\v2\x1b.islamic.v1.UmrahUrutanInfoR\x06urutan\"\x98\x01\n" +
	"\x14UmrahUrutanDetailRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x125\n" +
	"\x04data\x18\x04 \x01(\v2!.islamic.v1.UmrahUrutanDetailDataR\x04data\"\x17\n" +
	"\x15HajiDoaRingkasListReq\"\x91\x02\n" +
	"\x19HajiDoaRingkasContentInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12#\n" +
	"\rcontent_order\x18\x02 \x01(\x05R\fcontentOrder\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x1d\n" +
	"\n" +
	"muqatta_at\x18\x04 \x01(\tR\tmuqattaAt\x12\x1f\n" +
	"\varabic_text\x18\x05 \x01(\tR\n" +
	"arabicText\x12'\n" +
	"\x0findonesian_text\x18\x06 \x01(\tR\x0eindonesianText\x12\x1d\n" +
	"\n" +
	"latin_text\x18\a \x01(\tR\tlatinText\x12!\n" +
	"\fcontent_type\x18\b \x01(\x05R\vcontentType\"\xd9\x01\n" +
	"\x12HajiDoaRingkasInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x15\n" +
	"\x06doa_no\x18\x02 \x01(\x05R\x05doaNo\x12\x19\n" +
	"\bdoa_name\x18\x03 \x01(\tR\adoaName\x12A\n" +
	"\bcontents\x18\x04 \x03(\v2%.islamic.v1.HajiDoaRingkasContentInfoR\bcontents\x12\x1b\n" +
	"\tbacaan_id\x18\x05 \x01(\x04R\bbacaanId\x12!\n" +
	"\fis_collected\x18\x06 \x01(\bR\visCollected\"L\n" +
	"\x16HajiDoaRingkasListData\x122\n" +
	"\x04list\x18\x01 \x03(\v2\x1e.islamic.v1.HajiDoaRingkasInfoR\x04list\"\x9a\x01\n" +
	"\x15HajiDoaRingkasListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x126\n" +
	"\x04data\x18\x04 \x01(\v2\".islamic.v1.HajiDoaRingkasListDataR\x04data\"\x17\n" +
	"\x15HajiDoaPanjangListReq\"y\n" +
	"\x12HajiDoaPanjangInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x15\n" +
	"\x06doa_no\x18\x02 \x01(\x05R\x05doaNo\x12\x19\n" +
	"\bdoa_name\x18\x03 \x01(\tR\adoaName\x12!\n" +
	"\fbacaan_count\x18\x04 \x01(\x05R\vbacaanCount\"L\n" +
	"\x16HajiDoaPanjangListData\x122\n" +
	"\x04list\x18\x01 \x03(\v2\x1e.islamic.v1.HajiDoaPanjangInfoR\x04list\"\x9a\x01\n" +
	"\x15HajiDoaPanjangListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x126\n" +
	"\x04data\x18\x04 \x01(\v2\".islamic.v1.HajiDoaPanjangListDataR\x04data\"4\n" +
	"\x1bHajiDoaPanjangBacaanListReq\x12\x15\n" +
	"\x06doa_id\x18\x01 \x01(\x04R\x05doaId\"\x97\x02\n" +
	"\x1fHajiDoaPanjangBacaanContentInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12#\n" +
	"\rcontent_order\x18\x02 \x01(\x05R\fcontentOrder\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x1d\n" +
	"\n" +
	"muqatta_at\x18\x04 \x01(\tR\tmuqattaAt\x12\x1f\n" +
	"\varabic_text\x18\x05 \x01(\tR\n" +
	"arabicText\x12'\n" +
	"\x0findonesian_text\x18\x06 \x01(\tR\x0eindonesianText\x12\x1d\n" +
	"\n" +
	"latin_text\x18\a \x01(\tR\tlatinText\x12!\n" +
	"\fcontent_type\x18\b \x01(\x05R\vcontentType\"\x88\x02\n" +
	"\x18HajiDoaPanjangBacaanInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x15\n" +
	"\x06doa_id\x18\x02 \x01(\x04R\x05doaId\x12\x1b\n" +
	"\tbacaan_no\x18\x03 \x01(\x05R\bbacaanNo\x12\x1f\n" +
	"\vbacaan_name\x18\x04 \x01(\tR\n" +
	"bacaanName\x12G\n" +
	"\bcontents\x18\x05 \x03(\v2+.islamic.v1.HajiDoaPanjangBacaanContentInfoR\bcontents\x12\x1b\n" +
	"\tbacaan_id\x18\x06 \x01(\x04R\bbacaanId\x12!\n" +
	"\fis_collected\x18\a \x01(\bR\visCollected\"\x8a\x01\n" +
	"\x1cHajiDoaPanjangBacaanListData\x128\n" +
	"\x04list\x18\x01 \x03(\v2$.islamic.v1.HajiDoaPanjangBacaanInfoR\x04list\x12\x15\n" +
	"\x06doa_no\x18\x02 \x01(\x05R\x05doaNo\x12\x19\n" +
	"\bdoa_name\x18\x03 \x01(\tR\adoaName\"\xa6\x01\n" +
	"\x1bHajiDoaPanjangBacaanListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12<\n" +
	"\x04data\x18\x04 \x01(\v2(.islamic.v1.HajiDoaPanjangBacaanListDataR\x04data\"\x18\n" +
	"\x16UmrahDoaRingkasListReq\"\x92\x02\n" +
	"\x1aUmrahDoaRingkasContentInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12#\n" +
	"\rcontent_order\x18\x02 \x01(\x05R\fcontentOrder\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x1d\n" +
	"\n" +
	"muqatta_at\x18\x04 \x01(\tR\tmuqattaAt\x12\x1f\n" +
	"\varabic_text\x18\x05 \x01(\tR\n" +
	"arabicText\x12'\n" +
	"\x0findonesian_text\x18\x06 \x01(\tR\x0eindonesianText\x12\x1d\n" +
	"\n" +
	"latin_text\x18\a \x01(\tR\tlatinText\x12!\n" +
	"\fcontent_type\x18\b \x01(\x05R\vcontentType\"\xdb\x01\n" +
	"\x13UmrahDoaRingkasInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x15\n" +
	"\x06doa_no\x18\x02 \x01(\x05R\x05doaNo\x12\x19\n" +
	"\bdoa_name\x18\x03 \x01(\tR\adoaName\x12B\n" +
	"\bcontents\x18\x04 \x03(\v2&.islamic.v1.UmrahDoaRingkasContentInfoR\bcontents\x12\x1b\n" +
	"\tbacaan_id\x18\x05 \x01(\x04R\bbacaanId\x12!\n" +
	"\fis_collected\x18\x06 \x01(\bR\visCollected\"N\n" +
	"\x17UmrahDoaRingkasListData\x123\n" +
	"\x04list\x18\x01 \x03(\v2\x1f.islamic.v1.UmrahDoaRingkasInfoR\x04list\"\x9c\x01\n" +
	"\x16UmrahDoaRingkasListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x127\n" +
	"\x04data\x18\x04 \x01(\v2#.islamic.v1.UmrahDoaRingkasListDataR\x04data\"\x18\n" +
	"\x16UmrahDoaPanjangListReq\"z\n" +
	"\x13UmrahDoaPanjangInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x15\n" +
	"\x06doa_no\x18\x02 \x01(\x05R\x05doaNo\x12\x19\n" +
	"\bdoa_name\x18\x03 \x01(\tR\adoaName\x12!\n" +
	"\fbacaan_count\x18\x04 \x01(\x05R\vbacaanCount\"N\n" +
	"\x17UmrahDoaPanjangListData\x123\n" +
	"\x04list\x18\x01 \x03(\v2\x1f.islamic.v1.UmrahDoaPanjangInfoR\x04list\"\x9c\x01\n" +
	"\x16UmrahDoaPanjangListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x127\n" +
	"\x04data\x18\x04 \x01(\v2#.islamic.v1.UmrahDoaPanjangListDataR\x04data\"5\n" +
	"\x1cUmrahDoaPanjangBacaanListReq\x12\x15\n" +
	"\x06doa_id\x18\x01 \x01(\x04R\x05doaId\"\x98\x02\n" +
	" UmrahDoaPanjangBacaanContentInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12#\n" +
	"\rcontent_order\x18\x02 \x01(\x05R\fcontentOrder\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x1d\n" +
	"\n" +
	"muqatta_at\x18\x04 \x01(\tR\tmuqattaAt\x12\x1f\n" +
	"\varabic_text\x18\x05 \x01(\tR\n" +
	"arabicText\x12'\n" +
	"\x0findonesian_text\x18\x06 \x01(\tR\x0eindonesianText\x12\x1d\n" +
	"\n" +
	"latin_text\x18\a \x01(\tR\tlatinText\x12!\n" +
	"\fcontent_type\x18\b \x01(\x05R\vcontentType\"\x8a\x02\n" +
	"\x19UmrahDoaPanjangBacaanInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x15\n" +
	"\x06doa_id\x18\x02 \x01(\x04R\x05doaId\x12\x1b\n" +
	"\tbacaan_no\x18\x03 \x01(\x05R\bbacaanNo\x12\x1f\n" +
	"\vbacaan_name\x18\x04 \x01(\tR\n" +
	"bacaanName\x12H\n" +
	"\bcontents\x18\x05 \x03(\v2,.islamic.v1.UmrahDoaPanjangBacaanContentInfoR\bcontents\x12\x1b\n" +
	"\tbacaan_id\x18\x06 \x01(\x04R\bbacaanId\x12!\n" +
	"\fis_collected\x18\a \x01(\bR\visCollected\"\x8c\x01\n" +
	"\x1dUmrahDoaPanjangBacaanListData\x129\n" +
	"\x04list\x18\x01 \x03(\v2%.islamic.v1.UmrahDoaPanjangBacaanInfoR\x04list\x12\x15\n" +
	"\x06doa_no\x18\x02 \x01(\x05R\x05doaNo\x12\x19\n" +
	"\bdoa_name\x18\x03 \x01(\tR\adoaName\"\xa8\x01\n" +
	"\x1cUmrahDoaPanjangBacaanListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12=\n" +
	"\x04data\x18\x04 \x01(\v2).islamic.v1.UmrahDoaPanjangBacaanListDataR\x04data\"\x13\n" +
	"\x11HajiHikmahListReq\"U\n" +
	"\x0eHajiHikmahInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1d\n" +
	"\n" +
	"article_id\x18\x02 \x01(\x04R\tarticleId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\"D\n" +
	"\x12HajiHikmahListData\x12.\n" +
	"\x04list\x18\x01 \x03(\v2\x1a.islamic.v1.HajiHikmahInfoR\x04list\"\x92\x01\n" +
	"\x11HajiHikmahListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x122\n" +
	"\x04data\x18\x04 \x01(\v2\x1e.islamic.v1.HajiHikmahListDataR\x04data\"\x14\n" +
	"\x12UmrahHikmahListReq\"V\n" +
	"\x0fUmrahHikmahInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1d\n" +
	"\n" +
	"article_id\x18\x02 \x01(\x04R\tarticleId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\"F\n" +
	"\x13UmrahHikmahListData\x12/\n" +
	"\x04list\x18\x01 \x03(\v2\x1b.islamic.v1.UmrahHikmahInfoR\x04list\"\x94\x01\n" +
	"\x12UmrahHikmahListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x123\n" +
	"\x04data\x18\x04 \x01(\v2\x1f.islamic.v1.UmrahHikmahListDataR\x04data\"]\n" +
	"\x13HajiLandmarkListReq\x12\x1d\n" +
	"\n" +
	"inner_type\x18\x01 \x01(\tR\tinnerType\x12'\n" +
	"\x04page\x18\x02 \x01(\v2\x13.common.PageRequestR\x04page\"\xf0\x01\n" +
	"\x10HajiLandmarkItem\x12\x1f\n" +
	"\vlandmark_id\x18\x01 \x01(\x04R\n" +
	"landmarkId\x12\"\n" +
	"\rtype_icon_url\x18\x02 \x01(\tR\vtypeIconUrl\x12\x1b\n" +
	"\ttype_name\x18\x03 \x01(\tR\btypeName\x12\x1a\n" +
	"\blatitude\x18\x04 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x05 \x01(\x01R\tlongitude\x12\x1b\n" +
	"\timage_url\x18\x06 \x01(\tR\bimageUrl\x12#\n" +
	"\rlandmark_name\x18\a \x01(\tR\flandmarkName\"\xfc\x02\n" +
	"\x10HajiLandmarkInfo\x12\x1f\n" +
	"\vlandmark_id\x18\x01 \x01(\x04R\n" +
	"landmarkId\x12\"\n" +
	"\rtype_icon_url\x18\x02 \x01(\tR\vtypeIconUrl\x12\x1b\n" +
	"\ttype_name\x18\x03 \x01(\tR\btypeName\x12\x1a\n" +
	"\blatitude\x18\x04 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x05 \x01(\x01R\tlongitude\x12\x1b\n" +
	"\timage_url\x18\x06 \x01(\tR\bimageUrl\x12#\n" +
	"\rlandmark_name\x18\a \x01(\tR\flandmarkName\x12\x18\n" +
	"\acountry\x18\b \x01(\tR\acountry\x12\x18\n" +
	"\aaddress\x18\t \x01(\tR\aaddress\x12+\n" +
	"\x11short_description\x18\n" +
	" \x01(\tR\x10shortDescription\x12)\n" +
	"\x10information_text\x18\v \x01(\tR\x0finformationText\"r\n" +
	"\x14HajiLandmarkListData\x120\n" +
	"\x04list\x18\x01 \x03(\v2\x1c.islamic.v1.HajiLandmarkItemR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x96\x01\n" +
	"\x13HajiLandmarkListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x124\n" +
	"\x04data\x18\x04 \x01(\v2 .islamic.v1.HajiLandmarkListDataR\x04data\"8\n" +
	"\x15HajiLandmarkDetailReq\x12\x1f\n" +
	"\vlandmark_id\x18\x01 \x01(\x04R\n" +
	"landmarkId\"R\n" +
	"\x16HajiLandmarkDetailData\x128\n" +
	"\blandmark\x18\x01 \x01(\v2\x1c.islamic.v1.HajiLandmarkInfoR\blandmark\"\x9a\x01\n" +
	"\x15HajiLandmarkDetailRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x126\n" +
	"\x04data\x18\x04 \x01(\v2\".islamic.v1.HajiLandmarkDetailDataR\x04data\"^\n" +
	"\x14UmrahLandmarkListReq\x12\x1d\n" +
	"\n" +
	"inner_type\x18\x01 \x01(\tR\tinnerType\x12'\n" +
	"\x04page\x18\x02 \x01(\v2\x13.common.PageRequestR\x04page\"\xf1\x01\n" +
	"\x11UmrahLandmarkItem\x12\x1f\n" +
	"\vlandmark_id\x18\x01 \x01(\x04R\n" +
	"landmarkId\x12\"\n" +
	"\rtype_icon_url\x18\x02 \x01(\tR\vtypeIconUrl\x12\x1b\n" +
	"\ttype_name\x18\x03 \x01(\tR\btypeName\x12\x1a\n" +
	"\blatitude\x18\x04 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x05 \x01(\x01R\tlongitude\x12\x1b\n" +
	"\timage_url\x18\x06 \x01(\tR\bimageUrl\x12#\n" +
	"\rlandmark_name\x18\a \x01(\tR\flandmarkName\"\xfd\x02\n" +
	"\x11UmrahLandmarkInfo\x12\x1f\n" +
	"\vlandmark_id\x18\x01 \x01(\x04R\n" +
	"landmarkId\x12\"\n" +
	"\rtype_icon_url\x18\x02 \x01(\tR\vtypeIconUrl\x12\x1b\n" +
	"\ttype_name\x18\x03 \x01(\tR\btypeName\x12\x1a\n" +
	"\blatitude\x18\x04 \x01(\x01R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x05 \x01(\x01R\tlongitude\x12\x1b\n" +
	"\timage_url\x18\x06 \x01(\tR\bimageUrl\x12#\n" +
	"\rlandmark_name\x18\a \x01(\tR\flandmarkName\x12\x18\n" +
	"\acountry\x18\b \x01(\tR\acountry\x12\x18\n" +
	"\aaddress\x18\t \x01(\tR\aaddress\x12+\n" +
	"\x11short_description\x18\n" +
	" \x01(\tR\x10shortDescription\x12)\n" +
	"\x10information_text\x18\v \x01(\tR\x0finformationText\"t\n" +
	"\x15UmrahLandmarkListData\x121\n" +
	"\x04list\x18\x01 \x03(\v2\x1d.islamic.v1.UmrahLandmarkItemR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x98\x01\n" +
	"\x14UmrahLandmarkListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x125\n" +
	"\x04data\x18\x04 \x01(\v2!.islamic.v1.UmrahLandmarkListDataR\x04data\"9\n" +
	"\x16UmrahLandmarkDetailReq\x12\x1f\n" +
	"\vlandmark_id\x18\x01 \x01(\x04R\n" +
	"landmarkId\"T\n" +
	"\x17UmrahLandmarkDetailData\x129\n" +
	"\blandmark\x18\x01 \x01(\v2\x1d.islamic.v1.UmrahLandmarkInfoR\blandmark\"\x9c\x01\n" +
	"\x16UmrahLandmarkDetailRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x127\n" +
	"\x04data\x18\x04 \x01(\v2#.islamic.v1.UmrahLandmarkDetailDataR\x04data\":\n" +
	"\x0fHajiNewsListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"\xac\x01\n" +
	"\fHajiNewsItem\x12\x1d\n" +
	"\n" +
	"article_id\x18\x01 \x01(\x04R\tarticleId\x12#\n" +
	"\rcategory_name\x18\x02 \x01(\tR\fcategoryName\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12!\n" +
	"\fpublish_time\x18\x04 \x01(\x03R\vpublishTime\x12\x1f\n" +
	"\vcover_image\x18\x05 \x01(\tR\n" +
	"coverImage\"\x89\x01\n" +
	"\x10HajiNewsListData\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.islamic.v1.HajiNewsItemR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\x12\x1d\n" +
	"\n" +
	"header_tag\x18\x03 \x01(\tR\theaderTag\"\x8e\x01\n" +
	"\x0fHajiNewsListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x120\n" +
	"\x04data\x18\x04 \x01(\v2\x1c.islamic.v1.HajiNewsListDataR\x04data\"\x14\n" +
	"\x12RamadhanDoaListReq\"\x8e\x02\n" +
	"\x16RamadhanDoaContentInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12#\n" +
	"\rcontent_order\x18\x02 \x01(\x05R\fcontentOrder\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x1d\n" +
	"\n" +
	"muqatta_at\x18\x04 \x01(\tR\tmuqattaAt\x12\x1f\n" +
	"\varabic_text\x18\x05 \x01(\tR\n" +
	"arabicText\x12'\n" +
	"\x0findonesian_text\x18\x06 \x01(\tR\x0eindonesianText\x12\x1d\n" +
	"\n" +
	"latin_text\x18\a \x01(\tR\tlatinText\x12!\n" +
	"\fcontent_type\x18\b \x01(\x05R\vcontentType\"\xd3\x01\n" +
	"\x0fRamadhanDoaInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x15\n" +
	"\x06doa_no\x18\x02 \x01(\x05R\x05doaNo\x12\x19\n" +
	"\bdoa_name\x18\x03 \x01(\tR\adoaName\x12>\n" +
	"\bcontents\x18\x04 \x03(\v2\".islamic.v1.RamadhanDoaContentInfoR\bcontents\x12\x1b\n" +
	"\tbacaan_id\x18\x05 \x01(\x04R\bbacaanId\x12!\n" +
	"\fis_collected\x18\x06 \x01(\bR\visCollected\"F\n" +
	"\x13RamadhanDoaListData\x12/\n" +
	"\x04list\x18\x01 \x03(\v2\x1b.islamic.v1.RamadhanDoaInfoR\x04list\"\x94\x01\n" +
	"\x12RamadhanDoaListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x123\n" +
	"\x04data\x18\x04 \x01(\v2\x1f.islamic.v1.RamadhanDoaListDataR\x04data\"7\n" +
	"\x18CommonDoaBacaanDetailReq\x12\x1b\n" +
	"\tbacaan_id\x18\x01 \x01(\x04R\bbacaanId\"\xa4\x01\n" +
	"\x16CommonDoaBacaanContent\x12\x1f\n" +
	"\varabic_text\x18\x01 \x01(\tR\n" +
	"arabicText\x12'\n" +
	"\x0findonesian_text\x18\x02 \x01(\tR\x0eindonesianText\x12\x1d\n" +
	"\n" +
	"latin_text\x18\x03 \x01(\tR\tlatinText\x12!\n" +
	"\fcontent_type\x18\x04 \x01(\x05R\vcontentType\"P\n" +
	"\x19CommonDoaBacaanDetailData\x123\n" +
	"\x04list\x18\x01 \x03(\v2\x1f.islamic.v1.CommonDoaBacaanDataR\x04list\"\xd3\x01\n" +
	"\x13CommonDoaBacaanData\x12\x1b\n" +
	"\tbacaan_id\x18\x01 \x01(\x04R\bbacaanId\x12\x1b\n" +
	"\tbacaan_no\x18\x02 \x01(\x05R\bbacaanNo\x12\x1f\n" +
	"\vbacaan_name\x18\x03 \x01(\tR\n" +
	"bacaanName\x12!\n" +
	"\fis_collected\x18\x04 \x01(\bR\visCollected\x12>\n" +
	"\bcontents\x18\x05 \x03(\v2\".islamic.v1.CommonDoaBacaanContentR\bcontents\"\xa0\x01\n" +
	"\x18CommonDoaBacaanDetailRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x129\n" +
	"\x04data\x18\x04 \x01(\v2%.islamic.v1.CommonDoaBacaanDetailDataR\x04data2\xfc\x11\n" +
	"\rPrayerService\x12?\n" +
	"\vGetCalendar\x12\x17.islamic.v1.CalendarReq\x1a\x17.islamic.v1.CalendarRes\x12N\n" +
	"\x10GetBatchCalendar\x12\x1c.islamic.v1.BatchCalendarReq\x1a\x1c.islamic.v1.BatchCalendarRes\x12Z\n" +
	"\x12GetDailyPrayerTime\x12!.islamic.v1.GetDailyPrayerTimeReq\x1a!.islamic.v1.GetDailyPrayerTimeRes\x12c\n" +
	"\x15GetMonthlyPrayerTimes\x12$.islamic.v1.GetMonthlyPrayerTimesReq\x1a$.islamic.v1.GetMonthlyPrayerTimesRes\x12Q\n" +
	"\x11GetHajiJadwalList\x12\x1d.islamic.v1.HajiJadwalListReq\x1a\x1d.islamic.v1.HajiJadwalListRes\x12W\n" +
	"\x13GetHajiJadwalDetail\x12\x1f.islamic.v1.HajiJadwalDetailReq\x1a\x1f.islamic.v1.HajiJadwalDetailRes\x12Q\n" +
	"\x11GetHajiUrutanList\x12\x1d.islamic.v1.HajiUrutanListReq\x1a\x1d.islamic.v1.HajiUrutanListRes\x12W\n" +
	"\x13GetHajiUrutanDetail\x12\x1f.islamic.v1.HajiUrutanDetailReq\x1a\x1f.islamic.v1.HajiUrutanDetailRes\x12]\n" +
	"\x15GetHajiDoaRingkasList\x12!.islamic.v1.HajiDoaRingkasListReq\x1a!.islamic.v1.HajiDoaRingkasListRes\x12]\n" +
	"\x15GetHajiDoaPanjangList\x12!.islamic.v1.HajiDoaPanjangListReq\x1a!.islamic.v1.HajiDoaPanjangListRes\x12o\n" +
	"\x1bGetHajiDoaPanjangBacaanList\x12'.islamic.v1.HajiDoaPanjangBacaanListReq\x1a'.islamic.v1.HajiDoaPanjangBacaanListRes\x12f\n" +
	"\x18GetCommonDoaBacaanDetail\x12$.islamic.v1.CommonDoaBacaanDetailReq\x1a$.islamic.v1.CommonDoaBacaanDetailRes\x12Q\n" +
	"\x11GetHajiHikmahList\x12\x1d.islamic.v1.HajiHikmahListReq\x1a\x1d.islamic.v1.HajiHikmahListRes\x12K\n" +
	"\x0fGetHajiNewsList\x12\x1b.islamic.v1.HajiNewsListReq\x1a\x1b.islamic.v1.HajiNewsListRes\x12W\n" +
	"\x13GetHajiLandmarkList\x12\x1f.islamic.v1.HajiLandmarkListReq\x1a\x1f.islamic.v1.HajiLandmarkListRes\x12]\n" +
	"\x15GetHajiLandmarkDetail\x12!.islamic.v1.HajiLandmarkDetailReq\x1a!.islamic.v1.HajiLandmarkDetailRes\x12T\n" +
	"\x12GetUmrahUrutanList\x12\x1e.islamic.v1.UmrahUrutanListReq\x1a\x1e.islamic.v1.UmrahUrutanListRes\x12Z\n" +
	"\x14GetUmrahUrutanDetail\x12 .islamic.v1.UmrahUrutanDetailReq\x1a .islamic.v1.UmrahUrutanDetailRes\x12`\n" +
	"\x16GetUmrahDoaRingkasList\x12\".islamic.v1.UmrahDoaRingkasListReq\x1a\".islamic.v1.UmrahDoaRingkasListRes\x12`\n" +
	"\x16GetUmrahDoaPanjangList\x12\".islamic.v1.UmrahDoaPanjangListReq\x1a\".islamic.v1.UmrahDoaPanjangListRes\x12r\n" +
	"\x1cGetUmrahDoaPanjangBacaanList\x12(.islamic.v1.UmrahDoaPanjangBacaanListReq\x1a(.islamic.v1.UmrahDoaPanjangBacaanListRes\x12T\n" +
	"\x12GetUmrahHikmahList\x12\x1e.islamic.v1.UmrahHikmahListReq\x1a\x1e.islamic.v1.UmrahHikmahListRes\x12Z\n" +
	"\x14GetUmrahLandmarkList\x12 .islamic.v1.UmrahLandmarkListReq\x1a .islamic.v1.UmrahLandmarkListRes\x12`\n" +
	"\x16GetUmrahLandmarkDetail\x12\".islamic.v1.UmrahLandmarkDetailReq\x1a\".islamic.v1.UmrahLandmarkDetailRes\x12T\n" +
	"\x12GetRamadhanDoaList\x12\x1e.islamic.v1.RamadhanDoaListReq\x1a\x1e.islamic.v1.RamadhanDoaListResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_prayer_proto_rawDescOnce sync.Once
	file_islamic_v1_prayer_proto_rawDescData []byte
)

func file_islamic_v1_prayer_proto_rawDescGZIP() []byte {
	file_islamic_v1_prayer_proto_rawDescOnce.Do(func() {
		file_islamic_v1_prayer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_prayer_proto_rawDesc), len(file_islamic_v1_prayer_proto_rawDesc)))
	})
	return file_islamic_v1_prayer_proto_rawDescData
}

var file_islamic_v1_prayer_proto_msgTypes = make([]protoimpl.MessageInfo, 104)
var file_islamic_v1_prayer_proto_goTypes = []any{
	(*CalendarReq)(nil),                      // 0: islamic.v1.CalendarReq
	(*BatchCalendarReq)(nil),                 // 1: islamic.v1.BatchCalendarReq
	(*CalendarDateInfo)(nil),                 // 2: islamic.v1.CalendarDateInfo
	(*CalendarEventInfo)(nil),                // 3: islamic.v1.CalendarEventInfo
	(*CalendarData)(nil),                     // 4: islamic.v1.CalendarData
	(*CalendarRes)(nil),                      // 5: islamic.v1.CalendarRes
	(*BatchCalendarRes)(nil),                 // 6: islamic.v1.BatchCalendarRes
	(*BatchCalendarData)(nil),                // 7: islamic.v1.BatchCalendarData
	(*GetDailyPrayerTimeReq)(nil),            // 8: islamic.v1.GetDailyPrayerTimeReq
	(*GetDailyPrayerTimeRes)(nil),            // 9: islamic.v1.GetDailyPrayerTimeRes
	(*PrayerTimeData)(nil),                   // 10: islamic.v1.PrayerTimeData
	(*PrayerTime)(nil),                       // 11: islamic.v1.PrayerTime
	(*IslamicDate)(nil),                      // 12: islamic.v1.IslamicDate
	(*GetMonthlyPrayerTimesReq)(nil),         // 13: islamic.v1.GetMonthlyPrayerTimesReq
	(*GetMonthlyPrayerTimesRes)(nil),         // 14: islamic.v1.GetMonthlyPrayerTimesRes
	(*MonthlyPrayerTimesData)(nil),           // 15: islamic.v1.MonthlyPrayerTimesData
	(*HajiJadwalListReq)(nil),                // 16: islamic.v1.HajiJadwalListReq
	(*HajiJadwalDetailReq)(nil),              // 17: islamic.v1.HajiJadwalDetailReq
	(*HajiJadwalInfo)(nil),                   // 18: islamic.v1.HajiJadwalInfo
	(*HajiJadwalListData)(nil),               // 19: islamic.v1.HajiJadwalListData
	(*HajiJadwalListRes)(nil),                // 20: islamic.v1.HajiJadwalListRes
	(*HajiJadwalDetailData)(nil),             // 21: islamic.v1.HajiJadwalDetailData
	(*HajiJadwalDetailRes)(nil),              // 22: islamic.v1.HajiJadwalDetailRes
	(*HajiUrutanListReq)(nil),                // 23: islamic.v1.HajiUrutanListReq
	(*HajiUrutanDetailReq)(nil),              // 24: islamic.v1.HajiUrutanDetailReq
	(*HajiUrutanInfo)(nil),                   // 25: islamic.v1.HajiUrutanInfo
	(*HajiUrutanListData)(nil),               // 26: islamic.v1.HajiUrutanListData
	(*HajiUrutanListRes)(nil),                // 27: islamic.v1.HajiUrutanListRes
	(*HajiUrutanDetailData)(nil),             // 28: islamic.v1.HajiUrutanDetailData
	(*HajiUrutanDetailRes)(nil),              // 29: islamic.v1.HajiUrutanDetailRes
	(*UmrahUrutanListReq)(nil),               // 30: islamic.v1.UmrahUrutanListReq
	(*UmrahUrutanDetailReq)(nil),             // 31: islamic.v1.UmrahUrutanDetailReq
	(*UmrahUrutanInfo)(nil),                  // 32: islamic.v1.UmrahUrutanInfo
	(*UmrahUrutanListData)(nil),              // 33: islamic.v1.UmrahUrutanListData
	(*UmrahUrutanListRes)(nil),               // 34: islamic.v1.UmrahUrutanListRes
	(*UmrahUrutanDetailData)(nil),            // 35: islamic.v1.UmrahUrutanDetailData
	(*UmrahUrutanDetailRes)(nil),             // 36: islamic.v1.UmrahUrutanDetailRes
	(*HajiDoaRingkasListReq)(nil),            // 37: islamic.v1.HajiDoaRingkasListReq
	(*HajiDoaRingkasContentInfo)(nil),        // 38: islamic.v1.HajiDoaRingkasContentInfo
	(*HajiDoaRingkasInfo)(nil),               // 39: islamic.v1.HajiDoaRingkasInfo
	(*HajiDoaRingkasListData)(nil),           // 40: islamic.v1.HajiDoaRingkasListData
	(*HajiDoaRingkasListRes)(nil),            // 41: islamic.v1.HajiDoaRingkasListRes
	(*HajiDoaPanjangListReq)(nil),            // 42: islamic.v1.HajiDoaPanjangListReq
	(*HajiDoaPanjangInfo)(nil),               // 43: islamic.v1.HajiDoaPanjangInfo
	(*HajiDoaPanjangListData)(nil),           // 44: islamic.v1.HajiDoaPanjangListData
	(*HajiDoaPanjangListRes)(nil),            // 45: islamic.v1.HajiDoaPanjangListRes
	(*HajiDoaPanjangBacaanListReq)(nil),      // 46: islamic.v1.HajiDoaPanjangBacaanListReq
	(*HajiDoaPanjangBacaanContentInfo)(nil),  // 47: islamic.v1.HajiDoaPanjangBacaanContentInfo
	(*HajiDoaPanjangBacaanInfo)(nil),         // 48: islamic.v1.HajiDoaPanjangBacaanInfo
	(*HajiDoaPanjangBacaanListData)(nil),     // 49: islamic.v1.HajiDoaPanjangBacaanListData
	(*HajiDoaPanjangBacaanListRes)(nil),      // 50: islamic.v1.HajiDoaPanjangBacaanListRes
	(*UmrahDoaRingkasListReq)(nil),           // 51: islamic.v1.UmrahDoaRingkasListReq
	(*UmrahDoaRingkasContentInfo)(nil),       // 52: islamic.v1.UmrahDoaRingkasContentInfo
	(*UmrahDoaRingkasInfo)(nil),              // 53: islamic.v1.UmrahDoaRingkasInfo
	(*UmrahDoaRingkasListData)(nil),          // 54: islamic.v1.UmrahDoaRingkasListData
	(*UmrahDoaRingkasListRes)(nil),           // 55: islamic.v1.UmrahDoaRingkasListRes
	(*UmrahDoaPanjangListReq)(nil),           // 56: islamic.v1.UmrahDoaPanjangListReq
	(*UmrahDoaPanjangInfo)(nil),              // 57: islamic.v1.UmrahDoaPanjangInfo
	(*UmrahDoaPanjangListData)(nil),          // 58: islamic.v1.UmrahDoaPanjangListData
	(*UmrahDoaPanjangListRes)(nil),           // 59: islamic.v1.UmrahDoaPanjangListRes
	(*UmrahDoaPanjangBacaanListReq)(nil),     // 60: islamic.v1.UmrahDoaPanjangBacaanListReq
	(*UmrahDoaPanjangBacaanContentInfo)(nil), // 61: islamic.v1.UmrahDoaPanjangBacaanContentInfo
	(*UmrahDoaPanjangBacaanInfo)(nil),        // 62: islamic.v1.UmrahDoaPanjangBacaanInfo
	(*UmrahDoaPanjangBacaanListData)(nil),    // 63: islamic.v1.UmrahDoaPanjangBacaanListData
	(*UmrahDoaPanjangBacaanListRes)(nil),     // 64: islamic.v1.UmrahDoaPanjangBacaanListRes
	(*HajiHikmahListReq)(nil),                // 65: islamic.v1.HajiHikmahListReq
	(*HajiHikmahInfo)(nil),                   // 66: islamic.v1.HajiHikmahInfo
	(*HajiHikmahListData)(nil),               // 67: islamic.v1.HajiHikmahListData
	(*HajiHikmahListRes)(nil),                // 68: islamic.v1.HajiHikmahListRes
	(*UmrahHikmahListReq)(nil),               // 69: islamic.v1.UmrahHikmahListReq
	(*UmrahHikmahInfo)(nil),                  // 70: islamic.v1.UmrahHikmahInfo
	(*UmrahHikmahListData)(nil),              // 71: islamic.v1.UmrahHikmahListData
	(*UmrahHikmahListRes)(nil),               // 72: islamic.v1.UmrahHikmahListRes
	(*HajiLandmarkListReq)(nil),              // 73: islamic.v1.HajiLandmarkListReq
	(*HajiLandmarkItem)(nil),                 // 74: islamic.v1.HajiLandmarkItem
	(*HajiLandmarkInfo)(nil),                 // 75: islamic.v1.HajiLandmarkInfo
	(*HajiLandmarkListData)(nil),             // 76: islamic.v1.HajiLandmarkListData
	(*HajiLandmarkListRes)(nil),              // 77: islamic.v1.HajiLandmarkListRes
	(*HajiLandmarkDetailReq)(nil),            // 78: islamic.v1.HajiLandmarkDetailReq
	(*HajiLandmarkDetailData)(nil),           // 79: islamic.v1.HajiLandmarkDetailData
	(*HajiLandmarkDetailRes)(nil),            // 80: islamic.v1.HajiLandmarkDetailRes
	(*UmrahLandmarkListReq)(nil),             // 81: islamic.v1.UmrahLandmarkListReq
	(*UmrahLandmarkItem)(nil),                // 82: islamic.v1.UmrahLandmarkItem
	(*UmrahLandmarkInfo)(nil),                // 83: islamic.v1.UmrahLandmarkInfo
	(*UmrahLandmarkListData)(nil),            // 84: islamic.v1.UmrahLandmarkListData
	(*UmrahLandmarkListRes)(nil),             // 85: islamic.v1.UmrahLandmarkListRes
	(*UmrahLandmarkDetailReq)(nil),           // 86: islamic.v1.UmrahLandmarkDetailReq
	(*UmrahLandmarkDetailData)(nil),          // 87: islamic.v1.UmrahLandmarkDetailData
	(*UmrahLandmarkDetailRes)(nil),           // 88: islamic.v1.UmrahLandmarkDetailRes
	(*HajiNewsListReq)(nil),                  // 89: islamic.v1.HajiNewsListReq
	(*HajiNewsItem)(nil),                     // 90: islamic.v1.HajiNewsItem
	(*HajiNewsListData)(nil),                 // 91: islamic.v1.HajiNewsListData
	(*HajiNewsListRes)(nil),                  // 92: islamic.v1.HajiNewsListRes
	(*RamadhanDoaListReq)(nil),               // 93: islamic.v1.RamadhanDoaListReq
	(*RamadhanDoaContentInfo)(nil),           // 94: islamic.v1.RamadhanDoaContentInfo
	(*RamadhanDoaInfo)(nil),                  // 95: islamic.v1.RamadhanDoaInfo
	(*RamadhanDoaListData)(nil),              // 96: islamic.v1.RamadhanDoaListData
	(*RamadhanDoaListRes)(nil),               // 97: islamic.v1.RamadhanDoaListRes
	(*CommonDoaBacaanDetailReq)(nil),         // 98: islamic.v1.CommonDoaBacaanDetailReq
	(*CommonDoaBacaanContent)(nil),           // 99: islamic.v1.CommonDoaBacaanContent
	(*CommonDoaBacaanDetailData)(nil),        // 100: islamic.v1.CommonDoaBacaanDetailData
	(*CommonDoaBacaanData)(nil),              // 101: islamic.v1.CommonDoaBacaanData
	(*CommonDoaBacaanDetailRes)(nil),         // 102: islamic.v1.CommonDoaBacaanDetailRes
	nil,                                      // 103: islamic.v1.BatchCalendarData.CalendarsEntry
	(*common.Error)(nil),                     // 104: common.Error
	(*common.PageRequest)(nil),               // 105: common.PageRequest
	(*common.PageResponse)(nil),              // 106: common.PageResponse
}
var file_islamic_v1_prayer_proto_depIdxs = []int32{
	3,   // 0: islamic.v1.CalendarDateInfo.events:type_name -> islamic.v1.CalendarEventInfo
	2,   // 1: islamic.v1.CalendarData.list:type_name -> islamic.v1.CalendarDateInfo
	104, // 2: islamic.v1.CalendarRes.error:type_name -> common.Error
	4,   // 3: islamic.v1.CalendarRes.data:type_name -> islamic.v1.CalendarData
	104, // 4: islamic.v1.BatchCalendarRes.error:type_name -> common.Error
	7,   // 5: islamic.v1.BatchCalendarRes.data:type_name -> islamic.v1.BatchCalendarData
	103, // 6: islamic.v1.BatchCalendarData.calendars:type_name -> islamic.v1.BatchCalendarData.CalendarsEntry
	104, // 7: islamic.v1.GetDailyPrayerTimeRes.error:type_name -> common.Error
	10,  // 8: islamic.v1.GetDailyPrayerTimeRes.data:type_name -> islamic.v1.PrayerTimeData
	11,  // 9: islamic.v1.PrayerTimeData.prayer_time:type_name -> islamic.v1.PrayerTime
	12,  // 10: islamic.v1.PrayerTimeData.islamic_date:type_name -> islamic.v1.IslamicDate
	104, // 11: islamic.v1.GetMonthlyPrayerTimesRes.error:type_name -> common.Error
	15,  // 12: islamic.v1.GetMonthlyPrayerTimesRes.data:type_name -> islamic.v1.MonthlyPrayerTimesData
	10,  // 13: islamic.v1.MonthlyPrayerTimesData.list:type_name -> islamic.v1.PrayerTimeData
	18,  // 14: islamic.v1.HajiJadwalListData.list:type_name -> islamic.v1.HajiJadwalInfo
	104, // 15: islamic.v1.HajiJadwalListRes.error:type_name -> common.Error
	19,  // 16: islamic.v1.HajiJadwalListRes.data:type_name -> islamic.v1.HajiJadwalListData
	18,  // 17: islamic.v1.HajiJadwalDetailData.jadwal:type_name -> islamic.v1.HajiJadwalInfo
	104, // 18: islamic.v1.HajiJadwalDetailRes.error:type_name -> common.Error
	21,  // 19: islamic.v1.HajiJadwalDetailRes.data:type_name -> islamic.v1.HajiJadwalDetailData
	25,  // 20: islamic.v1.HajiUrutanListData.list:type_name -> islamic.v1.HajiUrutanInfo
	104, // 21: islamic.v1.HajiUrutanListRes.error:type_name -> common.Error
	26,  // 22: islamic.v1.HajiUrutanListRes.data:type_name -> islamic.v1.HajiUrutanListData
	25,  // 23: islamic.v1.HajiUrutanDetailData.urutan:type_name -> islamic.v1.HajiUrutanInfo
	104, // 24: islamic.v1.HajiUrutanDetailRes.error:type_name -> common.Error
	28,  // 25: islamic.v1.HajiUrutanDetailRes.data:type_name -> islamic.v1.HajiUrutanDetailData
	32,  // 26: islamic.v1.UmrahUrutanListData.list:type_name -> islamic.v1.UmrahUrutanInfo
	104, // 27: islamic.v1.UmrahUrutanListRes.error:type_name -> common.Error
	33,  // 28: islamic.v1.UmrahUrutanListRes.data:type_name -> islamic.v1.UmrahUrutanListData
	32,  // 29: islamic.v1.UmrahUrutanDetailData.urutan:type_name -> islamic.v1.UmrahUrutanInfo
	104, // 30: islamic.v1.UmrahUrutanDetailRes.error:type_name -> common.Error
	35,  // 31: islamic.v1.UmrahUrutanDetailRes.data:type_name -> islamic.v1.UmrahUrutanDetailData
	38,  // 32: islamic.v1.HajiDoaRingkasInfo.contents:type_name -> islamic.v1.HajiDoaRingkasContentInfo
	39,  // 33: islamic.v1.HajiDoaRingkasListData.list:type_name -> islamic.v1.HajiDoaRingkasInfo
	104, // 34: islamic.v1.HajiDoaRingkasListRes.error:type_name -> common.Error
	40,  // 35: islamic.v1.HajiDoaRingkasListRes.data:type_name -> islamic.v1.HajiDoaRingkasListData
	43,  // 36: islamic.v1.HajiDoaPanjangListData.list:type_name -> islamic.v1.HajiDoaPanjangInfo
	104, // 37: islamic.v1.HajiDoaPanjangListRes.error:type_name -> common.Error
	44,  // 38: islamic.v1.HajiDoaPanjangListRes.data:type_name -> islamic.v1.HajiDoaPanjangListData
	47,  // 39: islamic.v1.HajiDoaPanjangBacaanInfo.contents:type_name -> islamic.v1.HajiDoaPanjangBacaanContentInfo
	48,  // 40: islamic.v1.HajiDoaPanjangBacaanListData.list:type_name -> islamic.v1.HajiDoaPanjangBacaanInfo
	104, // 41: islamic.v1.HajiDoaPanjangBacaanListRes.error:type_name -> common.Error
	49,  // 42: islamic.v1.HajiDoaPanjangBacaanListRes.data:type_name -> islamic.v1.HajiDoaPanjangBacaanListData
	52,  // 43: islamic.v1.UmrahDoaRingkasInfo.contents:type_name -> islamic.v1.UmrahDoaRingkasContentInfo
	53,  // 44: islamic.v1.UmrahDoaRingkasListData.list:type_name -> islamic.v1.UmrahDoaRingkasInfo
	104, // 45: islamic.v1.UmrahDoaRingkasListRes.error:type_name -> common.Error
	54,  // 46: islamic.v1.UmrahDoaRingkasListRes.data:type_name -> islamic.v1.UmrahDoaRingkasListData
	57,  // 47: islamic.v1.UmrahDoaPanjangListData.list:type_name -> islamic.v1.UmrahDoaPanjangInfo
	104, // 48: islamic.v1.UmrahDoaPanjangListRes.error:type_name -> common.Error
	58,  // 49: islamic.v1.UmrahDoaPanjangListRes.data:type_name -> islamic.v1.UmrahDoaPanjangListData
	61,  // 50: islamic.v1.UmrahDoaPanjangBacaanInfo.contents:type_name -> islamic.v1.UmrahDoaPanjangBacaanContentInfo
	62,  // 51: islamic.v1.UmrahDoaPanjangBacaanListData.list:type_name -> islamic.v1.UmrahDoaPanjangBacaanInfo
	104, // 52: islamic.v1.UmrahDoaPanjangBacaanListRes.error:type_name -> common.Error
	63,  // 53: islamic.v1.UmrahDoaPanjangBacaanListRes.data:type_name -> islamic.v1.UmrahDoaPanjangBacaanListData
	66,  // 54: islamic.v1.HajiHikmahListData.list:type_name -> islamic.v1.HajiHikmahInfo
	104, // 55: islamic.v1.HajiHikmahListRes.error:type_name -> common.Error
	67,  // 56: islamic.v1.HajiHikmahListRes.data:type_name -> islamic.v1.HajiHikmahListData
	70,  // 57: islamic.v1.UmrahHikmahListData.list:type_name -> islamic.v1.UmrahHikmahInfo
	104, // 58: islamic.v1.UmrahHikmahListRes.error:type_name -> common.Error
	71,  // 59: islamic.v1.UmrahHikmahListRes.data:type_name -> islamic.v1.UmrahHikmahListData
	105, // 60: islamic.v1.HajiLandmarkListReq.page:type_name -> common.PageRequest
	74,  // 61: islamic.v1.HajiLandmarkListData.list:type_name -> islamic.v1.HajiLandmarkItem
	106, // 62: islamic.v1.HajiLandmarkListData.page:type_name -> common.PageResponse
	104, // 63: islamic.v1.HajiLandmarkListRes.error:type_name -> common.Error
	76,  // 64: islamic.v1.HajiLandmarkListRes.data:type_name -> islamic.v1.HajiLandmarkListData
	75,  // 65: islamic.v1.HajiLandmarkDetailData.landmark:type_name -> islamic.v1.HajiLandmarkInfo
	104, // 66: islamic.v1.HajiLandmarkDetailRes.error:type_name -> common.Error
	79,  // 67: islamic.v1.HajiLandmarkDetailRes.data:type_name -> islamic.v1.HajiLandmarkDetailData
	105, // 68: islamic.v1.UmrahLandmarkListReq.page:type_name -> common.PageRequest
	82,  // 69: islamic.v1.UmrahLandmarkListData.list:type_name -> islamic.v1.UmrahLandmarkItem
	106, // 70: islamic.v1.UmrahLandmarkListData.page:type_name -> common.PageResponse
	104, // 71: islamic.v1.UmrahLandmarkListRes.error:type_name -> common.Error
	84,  // 72: islamic.v1.UmrahLandmarkListRes.data:type_name -> islamic.v1.UmrahLandmarkListData
	83,  // 73: islamic.v1.UmrahLandmarkDetailData.landmark:type_name -> islamic.v1.UmrahLandmarkInfo
	104, // 74: islamic.v1.UmrahLandmarkDetailRes.error:type_name -> common.Error
	87,  // 75: islamic.v1.UmrahLandmarkDetailRes.data:type_name -> islamic.v1.UmrahLandmarkDetailData
	105, // 76: islamic.v1.HajiNewsListReq.page:type_name -> common.PageRequest
	90,  // 77: islamic.v1.HajiNewsListData.list:type_name -> islamic.v1.HajiNewsItem
	106, // 78: islamic.v1.HajiNewsListData.page:type_name -> common.PageResponse
	104, // 79: islamic.v1.HajiNewsListRes.error:type_name -> common.Error
	91,  // 80: islamic.v1.HajiNewsListRes.data:type_name -> islamic.v1.HajiNewsListData
	94,  // 81: islamic.v1.RamadhanDoaInfo.contents:type_name -> islamic.v1.RamadhanDoaContentInfo
	95,  // 82: islamic.v1.RamadhanDoaListData.list:type_name -> islamic.v1.RamadhanDoaInfo
	104, // 83: islamic.v1.RamadhanDoaListRes.error:type_name -> common.Error
	96,  // 84: islamic.v1.RamadhanDoaListRes.data:type_name -> islamic.v1.RamadhanDoaListData
	101, // 85: islamic.v1.CommonDoaBacaanDetailData.list:type_name -> islamic.v1.CommonDoaBacaanData
	99,  // 86: islamic.v1.CommonDoaBacaanData.contents:type_name -> islamic.v1.CommonDoaBacaanContent
	104, // 87: islamic.v1.CommonDoaBacaanDetailRes.error:type_name -> common.Error
	100, // 88: islamic.v1.CommonDoaBacaanDetailRes.data:type_name -> islamic.v1.CommonDoaBacaanDetailData
	4,   // 89: islamic.v1.BatchCalendarData.CalendarsEntry.value:type_name -> islamic.v1.CalendarData
	0,   // 90: islamic.v1.PrayerService.GetCalendar:input_type -> islamic.v1.CalendarReq
	1,   // 91: islamic.v1.PrayerService.GetBatchCalendar:input_type -> islamic.v1.BatchCalendarReq
	8,   // 92: islamic.v1.PrayerService.GetDailyPrayerTime:input_type -> islamic.v1.GetDailyPrayerTimeReq
	13,  // 93: islamic.v1.PrayerService.GetMonthlyPrayerTimes:input_type -> islamic.v1.GetMonthlyPrayerTimesReq
	16,  // 94: islamic.v1.PrayerService.GetHajiJadwalList:input_type -> islamic.v1.HajiJadwalListReq
	17,  // 95: islamic.v1.PrayerService.GetHajiJadwalDetail:input_type -> islamic.v1.HajiJadwalDetailReq
	23,  // 96: islamic.v1.PrayerService.GetHajiUrutanList:input_type -> islamic.v1.HajiUrutanListReq
	24,  // 97: islamic.v1.PrayerService.GetHajiUrutanDetail:input_type -> islamic.v1.HajiUrutanDetailReq
	37,  // 98: islamic.v1.PrayerService.GetHajiDoaRingkasList:input_type -> islamic.v1.HajiDoaRingkasListReq
	42,  // 99: islamic.v1.PrayerService.GetHajiDoaPanjangList:input_type -> islamic.v1.HajiDoaPanjangListReq
	46,  // 100: islamic.v1.PrayerService.GetHajiDoaPanjangBacaanList:input_type -> islamic.v1.HajiDoaPanjangBacaanListReq
	98,  // 101: islamic.v1.PrayerService.GetCommonDoaBacaanDetail:input_type -> islamic.v1.CommonDoaBacaanDetailReq
	65,  // 102: islamic.v1.PrayerService.GetHajiHikmahList:input_type -> islamic.v1.HajiHikmahListReq
	89,  // 103: islamic.v1.PrayerService.GetHajiNewsList:input_type -> islamic.v1.HajiNewsListReq
	73,  // 104: islamic.v1.PrayerService.GetHajiLandmarkList:input_type -> islamic.v1.HajiLandmarkListReq
	78,  // 105: islamic.v1.PrayerService.GetHajiLandmarkDetail:input_type -> islamic.v1.HajiLandmarkDetailReq
	30,  // 106: islamic.v1.PrayerService.GetUmrahUrutanList:input_type -> islamic.v1.UmrahUrutanListReq
	31,  // 107: islamic.v1.PrayerService.GetUmrahUrutanDetail:input_type -> islamic.v1.UmrahUrutanDetailReq
	51,  // 108: islamic.v1.PrayerService.GetUmrahDoaRingkasList:input_type -> islamic.v1.UmrahDoaRingkasListReq
	56,  // 109: islamic.v1.PrayerService.GetUmrahDoaPanjangList:input_type -> islamic.v1.UmrahDoaPanjangListReq
	60,  // 110: islamic.v1.PrayerService.GetUmrahDoaPanjangBacaanList:input_type -> islamic.v1.UmrahDoaPanjangBacaanListReq
	69,  // 111: islamic.v1.PrayerService.GetUmrahHikmahList:input_type -> islamic.v1.UmrahHikmahListReq
	81,  // 112: islamic.v1.PrayerService.GetUmrahLandmarkList:input_type -> islamic.v1.UmrahLandmarkListReq
	86,  // 113: islamic.v1.PrayerService.GetUmrahLandmarkDetail:input_type -> islamic.v1.UmrahLandmarkDetailReq
	93,  // 114: islamic.v1.PrayerService.GetRamadhanDoaList:input_type -> islamic.v1.RamadhanDoaListReq
	5,   // 115: islamic.v1.PrayerService.GetCalendar:output_type -> islamic.v1.CalendarRes
	6,   // 116: islamic.v1.PrayerService.GetBatchCalendar:output_type -> islamic.v1.BatchCalendarRes
	9,   // 117: islamic.v1.PrayerService.GetDailyPrayerTime:output_type -> islamic.v1.GetDailyPrayerTimeRes
	14,  // 118: islamic.v1.PrayerService.GetMonthlyPrayerTimes:output_type -> islamic.v1.GetMonthlyPrayerTimesRes
	20,  // 119: islamic.v1.PrayerService.GetHajiJadwalList:output_type -> islamic.v1.HajiJadwalListRes
	22,  // 120: islamic.v1.PrayerService.GetHajiJadwalDetail:output_type -> islamic.v1.HajiJadwalDetailRes
	27,  // 121: islamic.v1.PrayerService.GetHajiUrutanList:output_type -> islamic.v1.HajiUrutanListRes
	29,  // 122: islamic.v1.PrayerService.GetHajiUrutanDetail:output_type -> islamic.v1.HajiUrutanDetailRes
	41,  // 123: islamic.v1.PrayerService.GetHajiDoaRingkasList:output_type -> islamic.v1.HajiDoaRingkasListRes
	45,  // 124: islamic.v1.PrayerService.GetHajiDoaPanjangList:output_type -> islamic.v1.HajiDoaPanjangListRes
	50,  // 125: islamic.v1.PrayerService.GetHajiDoaPanjangBacaanList:output_type -> islamic.v1.HajiDoaPanjangBacaanListRes
	102, // 126: islamic.v1.PrayerService.GetCommonDoaBacaanDetail:output_type -> islamic.v1.CommonDoaBacaanDetailRes
	68,  // 127: islamic.v1.PrayerService.GetHajiHikmahList:output_type -> islamic.v1.HajiHikmahListRes
	92,  // 128: islamic.v1.PrayerService.GetHajiNewsList:output_type -> islamic.v1.HajiNewsListRes
	77,  // 129: islamic.v1.PrayerService.GetHajiLandmarkList:output_type -> islamic.v1.HajiLandmarkListRes
	80,  // 130: islamic.v1.PrayerService.GetHajiLandmarkDetail:output_type -> islamic.v1.HajiLandmarkDetailRes
	34,  // 131: islamic.v1.PrayerService.GetUmrahUrutanList:output_type -> islamic.v1.UmrahUrutanListRes
	36,  // 132: islamic.v1.PrayerService.GetUmrahUrutanDetail:output_type -> islamic.v1.UmrahUrutanDetailRes
	55,  // 133: islamic.v1.PrayerService.GetUmrahDoaRingkasList:output_type -> islamic.v1.UmrahDoaRingkasListRes
	59,  // 134: islamic.v1.PrayerService.GetUmrahDoaPanjangList:output_type -> islamic.v1.UmrahDoaPanjangListRes
	64,  // 135: islamic.v1.PrayerService.GetUmrahDoaPanjangBacaanList:output_type -> islamic.v1.UmrahDoaPanjangBacaanListRes
	72,  // 136: islamic.v1.PrayerService.GetUmrahHikmahList:output_type -> islamic.v1.UmrahHikmahListRes
	85,  // 137: islamic.v1.PrayerService.GetUmrahLandmarkList:output_type -> islamic.v1.UmrahLandmarkListRes
	88,  // 138: islamic.v1.PrayerService.GetUmrahLandmarkDetail:output_type -> islamic.v1.UmrahLandmarkDetailRes
	97,  // 139: islamic.v1.PrayerService.GetRamadhanDoaList:output_type -> islamic.v1.RamadhanDoaListRes
	115, // [115:140] is the sub-list for method output_type
	90,  // [90:115] is the sub-list for method input_type
	90,  // [90:90] is the sub-list for extension type_name
	90,  // [90:90] is the sub-list for extension extendee
	0,   // [0:90] is the sub-list for field type_name
}

func init() { file_islamic_v1_prayer_proto_init() }
func file_islamic_v1_prayer_proto_init() {
	if File_islamic_v1_prayer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_prayer_proto_rawDesc), len(file_islamic_v1_prayer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   104,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_prayer_proto_goTypes,
		DependencyIndexes: file_islamic_v1_prayer_proto_depIdxs,
		MessageInfos:      file_islamic_v1_prayer_proto_msgTypes,
	}.Build()
	File_islamic_v1_prayer_proto = out.File
	file_islamic_v1_prayer_proto_goTypes = nil
	file_islamic_v1_prayer_proto_depIdxs = nil
}
